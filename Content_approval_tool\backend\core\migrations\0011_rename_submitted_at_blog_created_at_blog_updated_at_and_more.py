# Generated by Django 5.2 on 2025-06-26 11:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0010_blog_review_comments"),
    ]

    operations = [
        migrations.RenameField(
            model_name="blog",
            old_name="submitted_at",
            new_name="created_at",
        ),
        migrations.AddField(
            model_name="blog",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="blog",
            name="status",
            field=models.CharField(
                choices=[
                    ("draft", "Draft"),
                    ("submitted", "Submitted"),
                    ("posted", "Posted"),
                    ("rejected", "Rejected"),
                    ("rework", "Rework"),
                    ("scheduled", "Scheduled"),
                ],
                default="pending",
                max_length=20,
            ),
        ),
    ]
