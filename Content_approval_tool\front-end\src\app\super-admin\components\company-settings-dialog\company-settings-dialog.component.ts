import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTabsModule } from '@angular/material/tabs';
import { MatListModule } from '@angular/material/list';

import { SuperAdminService } from '../../services/super-admin.service';
import { CompanySettings, ProjectCreationRequest, User, Project } from '../../models/super-admin.models';

@Component({
  selector: 'app-company-settings-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatChipsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTabsModule,
    MatListModule
  ],
  template: `
    <h2 mat-dialog-title>
      <mat-icon>settings</mat-icon>
      Company Settings - {{ companySettings?.company?.name }}
    </h2>

    <mat-dialog-content>
      <!-- Debug Info -->
      <div style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
        <strong>Debug Info:</strong><br>
        isLoading: {{ isLoading }}<br>
        companySettings: {{ companySettings ? 'Loaded' : 'Not loaded' }}<br>
        Company ID: {{ data.companyId }}
      </div>

      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading company settings...</p>
      </div>

      <div *ngIf="!isLoading && !companySettings" class="error-container">
        <mat-icon>error</mat-icon>
        <p>Failed to load company settings. Please try again.</p>
        <button mat-button (click)="loadCompanySettings()">Retry</button>
      </div>

      <div *ngIf="!isLoading && companySettings" class="settings-container">
        <mat-tab-group>
          <!-- Projects Tab -->
          <mat-tab label="Projects">
            <div class="tab-content">
              <!-- Create New Project Form -->
              <mat-card class="create-project-card">
                <mat-card-header>
                  <mat-card-title>Create New Project</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <form [formGroup]="projectForm" class="project-form">
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="half-width">
                        <mat-label>Project Name</mat-label>
                        <input matInput formControlName="name" placeholder="Enter project name">
                        <mat-error *ngIf="projectForm.get('name')?.hasError('required')">
                          Project name is required
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline" class="half-width">
                        <mat-label>Project Title</mat-label>
                        <input matInput formControlName="title" placeholder="Enter project title">
                        <mat-error *ngIf="projectForm.get('title')?.hasError('required')">
                          Project title is required
                        </mat-error>
                      </mat-form-field>
                    </div>

                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Description (Optional)</mat-label>
                      <textarea matInput formControlName="description" 
                                placeholder="Enter project description" 
                                rows="3"></textarea>
                    </mat-form-field>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="half-width">
                        <mat-label>Deadline (Optional)</mat-label>
                        <input matInput [matDatepicker]="picker" formControlName="deadline">
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                      </mat-form-field>

                      <mat-form-field appearance="outline" class="half-width">
                        <mat-label>Assign Company Admin</mat-label>
                        <mat-select formControlName="company_admin_id">
                          <mat-option value="">Select Admin</mat-option>
                          <mat-option *ngFor="let user of eligibleAdmins" [value]="user.id">
                            {{ user.username }} - {{ user.email }}
                            <span *ngIf="user.company" class="user-company-info"> ({{ user.company.name }})</span>
                          </mat-option>
                        </mat-select>
                        <mat-hint>{{ eligibleAdmins.length }} company admin(s) available</mat-hint>
                      </mat-form-field>
                    </div>

                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Assign Creators</mat-label>
                      <mat-select formControlName="creator_ids" multiple>
                        <mat-option *ngFor="let creator of availableCreators" [value]="creator.id">
                          {{ creator.username }} - {{ creator.email }}
                          <span *ngIf="creator.company" class="user-company-info"> ({{ creator.company.name }})</span>
                        </mat-option>
                      </mat-select>
                      <mat-hint>{{ availableCreators.length }} creator(s) available</mat-hint>
                    </mat-form-field>

                    <div class="form-actions">
                      <button mat-raised-button 
                              color="primary" 
                              (click)="createProject()"
                              [disabled]="projectForm.invalid || isCreatingProject">
                        <mat-icon>add</mat-icon>
                        Create Project
                      </button>
                    </div>
                  </form>
                </mat-card-content>
              </mat-card>

              <!-- Existing Projects List -->
              <mat-card class="projects-list-card">
                <mat-card-header>
                  <mat-card-title>Existing Projects</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div *ngIf="companySettings.projects.length === 0" class="empty-state">
                    <mat-icon>folder_open</mat-icon>
                    <p>No projects created yet</p>
                  </div>

                  <mat-list *ngIf="companySettings.projects.length > 0">
                    <mat-list-item *ngFor="let project of companySettings.projects">
                      <mat-icon matListItemIcon>folder</mat-icon>
                      <div matListItemTitle>{{ project.title || project.name }}</div>
                      <div matListItemLine>
                        <span>{{ project.description || 'No description' }}</span>
                        <span *ngIf="project.deadline"> • Deadline: {{ formatDate(project.deadline) }}</span>
                      </div>
                      <div matListItemLine>
                        <mat-chip-set>
                          <mat-chip *ngFor="let creator of project.creators">
                            {{ creator.username }}
                          </mat-chip>
                        </mat-chip-set>
                      </div>
                    </mat-list-item>
                  </mat-list>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>

          <!-- Company Admin Tab -->
          <mat-tab label="Company Admin">
            <div class="tab-content">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>Current Company Admin</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div *ngIf="companySettings.admin; else noAdmin">
                    <p><strong>Username:</strong> {{ companySettings.admin.username }}</p>
                    <p><strong>Email:</strong> {{ companySettings.admin.email }}</p>
                  </div>
                  <ng-template #noAdmin>
                    <p>No company admin assigned</p>
                  </ng-template>
                </mat-card-content>
              </mat-card>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Close</button>
    </mat-dialog-actions>
  `,
  styles: [`
    .settings-container {
      min-width: 600px;
      max-width: 800px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      gap: 16px;
    }

    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      gap: 16px;
      color: #f44336;
    }

    .tab-content {
      padding: 20px 0;
    }

    .project-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .half-width {
      flex: 1;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }

    .create-project-card {
      margin-bottom: 20px;
    }

    .projects-list-card {
      margin-top: 20px;
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .empty-state mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    mat-dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    mat-dialog-content {
      max-height: 70vh;
      overflow-y: auto;
    }

    .user-company-info {
      font-size: 12px;
      color: #666;
      font-style: italic;
    }
  `]
})
export class CompanySettingsDialogComponent implements OnInit {
  projectForm: FormGroup;
  companySettings: CompanySettings | null = null;
  isLoading = false;
  isCreatingProject = false;

  eligibleAdmins: User[] = [];
  availableCreators: User[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<CompanySettingsDialogComponent>,
    private superAdminService: SuperAdminService,
    @Inject(MAT_DIALOG_DATA) public data: { companyId: number }
  ) {
    this.projectForm = this.fb.group({
      name: ['', [Validators.required]],
      title: ['', [Validators.required]],
      description: [''],
      deadline: [''],
      company_admin_id: [''],
      creator_ids: [[]]
    });
  }

  ngOnInit(): void {
    this.loadCompanySettings();
  }

  loadCompanySettings(): void {
    console.log('Loading company settings for company ID:', this.data.companyId);
    this.isLoading = true;
    this.superAdminService.getCompanySettings(this.data.companyId).subscribe({
      next: (settings) => {
        console.log('Company settings loaded successfully:', settings);
        this.companySettings = settings;
        this.filterAvailableUsers();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading company settings:', error);
        this.isLoading = false;
      }
    });
  }

  filterAvailableUsers(): void {
    if (!this.companySettings) return;

    // Filter eligible admins (only users with company_admin role)
    this.eligibleAdmins = this.companySettings.available_users.filter(user =>
      user.role === 'company_admin'
    );

    // Filter available creators (only users with creator role)
    this.availableCreators = this.companySettings.available_users.filter(user =>
      user.role === 'creator'
    );

    console.log('Available users:', this.companySettings.available_users);
    console.log('Eligible admins found:', this.eligibleAdmins.length, this.eligibleAdmins);
    console.log('Available creators found:', this.availableCreators.length, this.availableCreators);
  }

  createProject(): void {
    if (this.projectForm.invalid || !this.companySettings) return;

    this.isCreatingProject = true;
    const formValue = this.projectForm.value;
    
    const projectData: ProjectCreationRequest = {
      name: formValue.name,
      title: formValue.title,
      description: formValue.description,
      deadline: formValue.deadline ? new Date(formValue.deadline).toISOString().split('T')[0] : undefined,
      company_id: this.companySettings.company.id,
      creator_ids: formValue.creator_ids,
      company_admin_id: formValue.company_admin_id || undefined
    };

    this.superAdminService.createProject(projectData).subscribe({
      next: (response) => {
        console.log('Project created successfully:', response);
        this.projectForm.reset();
        this.loadCompanySettings(); // Refresh the data
        this.isCreatingProject = false;
      },
      error: (error) => {
        console.error('Error creating project:', error);
        this.isCreatingProject = false;
      }
    });
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
