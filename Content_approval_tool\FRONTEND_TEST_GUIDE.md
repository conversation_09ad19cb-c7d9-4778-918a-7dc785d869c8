# Frontend Testing Guide - Content Approval Tool

## 🚀 Quick Start

### 1. Start the Servers

**Backend:**
```bash
cd backend
python manage.py runserver 8000
```

**Frontend:**
```bash
cd front-end
npm install  # if not already done
npm start
```

**Test Backend (Optional):**
```bash
python test_backend.py
```

### 2. Access the Application
- Open your browser to: `http://localhost:4200`
- Backend API: `http://localhost:8000/api`

## 🧪 Test Scenarios

### **Scenario 1: Thumbnail Display Fix** 🖼️

**Test Steps:**
1. Login as a Creator
2. Navigate to Calendar View
3. Create a new post with an image/video
4. Submit the post
5. **Expected Result**: Thumbnail should display in the calendar view

**What to Look For:**
- ✅ Image thumbnails show as preview images
- ✅ Video thumbnails show with play button overlay
- ✅ File attachments show with file icon
- ✅ No broken image icons

### **Scenario 2: Creator Name & Project Display** 👤

**Test Steps:**
1. Login as Creator
2. View calendar or post list
3. Hover over posts or view post details

**Expected Results:**
- ✅ Creator name displays on each post
- ✅ Project title shows correctly
- ✅ Company name appears in post metadata
- ✅ Tooltip shows complete information

### **Scenario 3: Authentication & 401 Error Fix** 🔐

**Test Steps:**
1. Login with valid credentials
2. Try to create/submit a post
3. Let the session expire (or manually remove token from localStorage)
4. Try to perform an action

**Expected Results:**
- ✅ No 401 errors on valid requests
- ✅ Automatic redirect to login on token expiry
- ✅ Token is properly attached to all API calls
- ✅ Clear error messages for authentication issues

### **Scenario 4: Approval Status Updates** 📊

**Test Steps:**
1. **As Creator**: Create and submit a post for review
2. **As Company Admin**: Login and view pending reviews
3. **As Company Admin**: Approve/Reject the post
4. **As Creator**: Check if status updated in real-time

**Expected Results:**
- ✅ Status changes from "draft" → "submitted" → "approved/rejected"
- ✅ Both Creator and Admin see updated status
- ✅ Status badges show correct colors and icons
- ✅ Real-time updates without page refresh

### **Scenario 5: Permission Matrix Enforcement** 🛡️

**Test Each Role:**

**Super Admin:**
- ✅ Can create companies
- ✅ Can create projects
- ✅ Can assign creators to projects
- ✅ Cannot review posts (Company Admin only)

**Company Admin:**
- ✅ Can create projects for their company
- ✅ Can review and approve/reject posts
- ✅ Can assign creators to projects
- ❌ Cannot create companies

**Creator:**
- ✅ Can create and submit posts
- ✅ Can only see assigned projects
- ❌ Cannot create projects
- ❌ Cannot review posts

### **Scenario 6: Kanban-Style Sidebar** 🎨

**Test Steps:**
1. Login as Creator
2. Check sidebar functionality

**Expected Results:**
- ✅ Material Design styling with proper colors
- ✅ Create Post button opens dialog (not navigation)
- ✅ Navigation icons with tooltips
- ✅ User avatar in bottom left
- ✅ Notification badges show pending counts
- ✅ Sidebar collapses on mobile
- ✅ Smooth animations and hover effects

## 🔍 Debugging Tips

### Check Browser Console
- Open Developer Tools (F12)
- Look for any JavaScript errors
- Check Network tab for failed API calls

### Common Issues & Solutions

**1. 401 Unauthorized Errors:**
- Check if token exists: `localStorage.getItem('access_token')`
- Verify token format in Network tab headers
- Clear localStorage and login again

**2. Thumbnails Not Loading:**
- Check if media files are uploaded correctly
- Verify backend media URL configuration
- Check browser console for image loading errors

**3. Status Not Updating:**
- Check if WebSocket connections are working
- Verify API endpoints are returning correct data
- Test manual page refresh to see if data persists

**4. Permission Errors:**
- Verify user role in JWT token
- Check if role-based guards are working
- Test with different user accounts

## 📱 Mobile Testing

**Test on Different Screen Sizes:**
- Desktop (1920x1080)
- Tablet (768x1024)
- Mobile (375x667)

**Expected Responsive Behavior:**
- ✅ Sidebar collapses on mobile
- ✅ Calendar view adapts to screen size
- ✅ Touch-friendly buttons and interactions
- ✅ Proper text scaling

## 🎯 Performance Testing

**Check for:**
- ✅ Fast initial load times
- ✅ Smooth animations
- ✅ Efficient API calls (no unnecessary requests)
- ✅ Proper loading states
- ✅ Image optimization

## 📊 Test Data Setup

**Create Test Users:**
```bash
cd backend
python manage.py createsuperuser  # Super Admin
```

**Create Test Data via Admin Panel:**
1. Go to `http://localhost:8000/admin`
2. Create Companies
3. Create Company Admin users
4. Create Creator users
5. Create Projects and assign creators

## ✅ Success Criteria

**All tests pass when:**
- 🖼️ Thumbnails display correctly in calendar
- 👤 Creator and project names show on posts
- 🔐 No authentication errors during normal use
- 📊 Status updates work in real-time
- 🛡️ Permission matrix is strictly enforced
- 🎨 Sidebar has proper Kanban-style design
- 📱 Application is fully responsive
- ⚡ Performance is smooth and fast

## 🐛 Bug Reporting

**If you find issues, please note:**
1. User role being tested
2. Browser and version
3. Steps to reproduce
4. Expected vs actual behavior
5. Console errors (if any)
6. Screenshots/videos if helpful

---

**Happy Testing! 🎉**

The Content Approval Tool should now be fully functional with all the requested fixes implemented.
