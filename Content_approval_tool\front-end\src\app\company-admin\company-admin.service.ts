import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DashboardStats, ContentPost, Creator, ActionResponse, AssignedProject, CreatorAssignmentRequest, ReviewActionRequest, Blog } from './models/dashboard.models';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CompanyAdminService {
  private baseUrl = 'http://127.0.0.1:8000/api/company-admin';
  private apiUrl = 'http://127.0.0.1:8000/api';

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  // Dashboard Statistics
  getDashboardStats(): Observable<DashboardStats> {
    return this.http.get<DashboardStats>(`${this.baseUrl}/dashboard_stats/`, {
      headers: this.getHeaders()
    });
  }

  // Recent Content
  getRecentContent(): Observable<(ContentPost & { type: 'post' } | Blog & { type: 'blog' })[]> {
    return this.http.get<any[]>(`${this.baseUrl}/recent_content/`, {
      headers: this.getHeaders()
    }).pipe(
      map(items => items.map(item => {
        if ('author_name' in item) {
          return { ...item, type: 'blog' } as Blog & { type: 'blog' };
        } else {
          return { ...item, type: 'post' } as ContentPost & { type: 'post' };
        }
      }))
    );
  }

  // Pending Reviews
  getPendingReviews(): Observable<(ContentPost & { type: 'post' } | Blog & { type: 'blog' })[]> {
    return this.http.get<any[]>(`${this.baseUrl}/pending_reviews/`, {
      headers: this.getHeaders()
    }).pipe(
      map(items => items.map(item => {
        if ('author_name' in item) {
          return { ...item, type: 'blog' } as Blog & { type: 'blog' };
        } else {
          return { ...item, type: 'post' } as ContentPost & { type: 'post' };
        }
      }))
    );
  }

  // Creators
  getCreators(): Observable<Creator[]> {
    return this.http.get<Creator[]>(`${this.baseUrl}/creators/`, {
      headers: this.getHeaders()
    });
  }

  // Content Actions
  approvePost(postId: number): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`${this.apiUrl}/posts/${postId}/approve_post/`, {}, {
      headers: this.getHeaders()
    });
  }

  rejectPost(postId: number, review_comments: string): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`${this.apiUrl}/posts/${postId}/reject_post/`,
      { review_comments }, {
      headers: this.getHeaders()
    });
  }

  requestChangesPost(postId: number, review_comments: string): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`${this.apiUrl}/posts/${postId}/request_changes_post/`,
      { review_comments }, {
      headers: this.getHeaders()
    });
  }

  // Project Management
  getAssignedProjects(): Observable<AssignedProject[]> {
    return this.http.get<AssignedProject[]>(`${this.baseUrl}/assigned_projects/`, {
      headers: this.getHeaders()
    });
  }

  getAvailableCreators(): Observable<Creator[]> {
    return this.http.get<Creator[]>(`${this.baseUrl}/available_creators/`, {
      headers: this.getHeaders()
    });
  }

  assignCreatorsToProject(projectId: number, request: CreatorAssignmentRequest): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`${this.baseUrl}/${projectId}/assign_creators/`, request, {
      headers: this.getHeaders()
    });
  }

  // Update Post Status (for real-time updates)
  updatePostStatus(postId: number, status: string): Observable<ActionResponse> {
    return this.http.patch<ActionResponse>(`${this.baseUrl}/${postId}/update_status/`, { status }, {
      headers: this.getHeaders()
    });
  }

  // Utility methods
  formatDate(dateString: string): string {
    if (!dateString) return 'No date';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid date';
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'submitted': return '#ff9800';
      case 'posted': return '#4caf50';
      case 'rejected': return '#f44336';
      case 'rework': return '#ff5722';
      case 'draft': return '#9e9e9e';
      case 'scheduled': return '#2196f3';
      default: return '#9e9e9e';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'submitted': return 'schedule';
      case 'posted': return 'check_circle';
      case 'rejected': return 'cancel';
      case 'rework': return 'edit';
      case 'draft': return 'draft';
      case 'scheduled': return 'event';
      default: return 'help';
    }
  }

  // Pending Blogs
  getPendingBlogs(): Observable<(ContentPost & { type: 'post' } | Blog & { type: 'blog' })[]> {
    return this.http.get<any[]>(`${this.baseUrl}/pending_blogs/`, {
      headers: this.getHeaders()
    }).pipe(
      map(items => items.map(item => ({ ...item, type: 'blog' }) as Blog & { type: 'blog' }))
    );
  }

  // Blog Review Actions
  approveBlog(blogId: number, comments?: string): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`http://127.0.0.1:8000/api/blogs/${blogId}/approve_blog/`,
      { comments: comments || '' }, {
      headers: this.getHeaders()
    });
  }

  rejectBlog(blogId: number, reason: string): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`http://127.0.0.1:8000/api/blogs/${blogId}/reject_blog/`,
      { reason }, {
      headers: this.getHeaders()
    });
  }

  requestBlogChanges(blogId: number, instructions: string): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`http://127.0.0.1:8000/api/blogs/${blogId}/changes_requested/`,
      { instructions }, {
      headers: this.getHeaders()
    });
  }

  requestBlogRework(blogId: number, instructions: string): Observable<ActionResponse> {
    return this.http.post<ActionResponse>(`http://127.0.0.1:8000/api/blogs/${blogId}/request_rework/`,
      { review_comment: instructions }, {
      headers: this.getHeaders()
    });
  }
}
