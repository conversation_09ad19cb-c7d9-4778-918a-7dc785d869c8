import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Post } from '../models/post.model';



@Injectable({
  providedIn: 'root'
})
export class PostService {
  private apiUrl = 'http://127.0.0.1:8000/api/posts/';
  private creatorApiUrl = 'http://127.0.0.1:8000/api/creator-dashboard/';

  constructor(private http: HttpClient) { }

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  private getAuthHeadersForFormData(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData, let browser set it with boundary
    });
  }
  getPosts(): Observable<Post[]> {
    return this.http.get<Post[]>(`${this.creatorApiUrl}my_posts/`, { headers: this.getAuthHeaders() });
  }

  getPost(id: number): Observable<Post> {
    return this.http.get<Post>(`${this.apiUrl}${id}/`, { headers: this.getAuthHeaders() });
  }

  createPostWithFiles(formData: FormData): Observable<Post> {
    return this.http.post<Post>(`${this.creatorApiUrl}upload_post/`, formData, { headers: this.getAuthHeadersForFormData() });
  }



  updatePost(id: number, postData: Post | FormData): Observable<Post> {
    const headers = postData instanceof FormData ? this.getAuthHeadersForFormData() : this.getAuthHeaders();
    // Use creator-specific update endpoint
    return this.http.put<Post>(`${this.creatorApiUrl}${id}/update_post/`, postData, { headers });
  }

  deletePost(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}${id}/`, { headers: this.getAuthHeaders() });
  }

  getAllPosts(): Observable<Post[]> {
    return this.http.get<Post[]>(`${this.creatorApiUrl}my_posts/`, { headers: this.getAuthHeaders() });
  }

  updatePostStatus(postId: number, status: string): Observable<any> {
    return this.http.patch(`${this.creatorApiUrl}${postId}/update_status/`,
      { status },
      { headers: this.getAuthHeaders() }
    );
  }

  submitPostForReview(postId: number): Observable<any> {
    return this.updatePostStatus(postId, 'submitted');
  }




}
