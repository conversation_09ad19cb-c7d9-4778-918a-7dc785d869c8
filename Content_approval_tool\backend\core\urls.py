from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    UserViewSet, CompanyViewSet, ProjectViewSet, PostViewSet,
    login_view, CustomTokenObtainPairView, SuperAdminViewSet, CreatorDashboardViewSet, CompanyAdminViewSet, BlogViewSet, get_current_user, get_user_posts,
    health_check, social_media_publish
)
from django.conf import settings
from django.conf.urls.static import static

# Main router for standard viewsets
main_router = DefaultRouter()
main_router.register(r'users', UserViewSet)
main_router.register(r'companies', CompanyViewSet)
main_router.register(r'projects', ProjectViewSet)
main_router.register(r'posts', PostViewSet)
main_router.register(r'blogs', BlogViewSet, basename='blogs')


# Separate router for super admin actions
super_admin_router = DefaultRouter()
super_admin_router.register(r'super-admin', SuperAdminViewSet, basename='super-admin')

creator_router = DefaultRouter()
creator_router.register(r'creator-dashboard', CreatorDashboardViewSet, basename='creator-dashboard')

company_admin_router = DefaultRouter()
company_admin_router.register(r'company-admin', CompanyAdminViewSet, basename='company-admin')


urlpatterns = [
    path('api/', include(main_router.urls)),
    path('api/', include(super_admin_router.urls)),
    path('api/', include(creator_router.urls)),
    path('api/', include(company_admin_router.urls)),
    path('api/login/', login_view, name='login'),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/me/', get_current_user),
    path('api/creator-dashboard/get_posts/', get_user_posts, name='get_user_posts'),
    path('api/social/publish/', social_media_publish, name='social_media_publish'),
    path('api/health/', health_check),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)