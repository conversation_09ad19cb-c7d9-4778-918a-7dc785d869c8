<div class="blog-create-container">
  <mat-card class="main-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>{{ isEditMode ? 'edit' : 'add_circle' }}</mat-icon>
        {{ isEditMode ? 'Edit Blog' : 'Create New Blog' }}
      </mat-card-title>
      <mat-card-subtitle>
        {{ isEditMode ? 'Update your blog content' : 'Create engaging blog content with our block-based editor' }}
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-icon class="loading-icon">hourglass_empty</mat-icon>
        <p>Loading...</p>
      </div>

      <div *ngIf="!isLoading && !showPreview">
        <!-- Review Feedback Section (for edit mode) -->
        <div *ngIf="isEditMode && originalBlog?.status === 'changes_requested'" class="review-feedback-section">
          <mat-card class="feedback-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon color="warn">feedback</mat-icon>
                Review Feedback
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="feedback-message">
                <p><strong>Company Admin Comments:</strong></p>
                <p>{{ originalBlog?.review_comments || 'No specific comments provided.' }}</p>
              </div>
              <div class="feedback-action">
                <mat-icon color="primary">info</mat-icon>
                <span>Please address the feedback above and resubmit your blog.</span>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <mat-tab-group>
          <!-- Basic Information Tab -->
          <mat-tab label="Basic Info">
            <div class="tab-content">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Blog Title</mat-label>
                <input matInput [(ngModel)]="blog.title" required placeholder="Enter blog title...">
                <mat-icon matSuffix>title</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Reference Scripture (optional)</mat-label>
                <input matInput [(ngModel)]="blog.scripture" placeholder="Enter scripture reference...">
                <mat-icon matSuffix>book</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Project</mat-label>
                <mat-select [(ngModel)]="blog.project" required>
                  <mat-option *ngFor="let project of assignedProjects" [value]="project.id">
                    {{ project.title || project.name }} ({{ project.company.name }})
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>folder</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Tags</mat-label>
                <input matInput [(ngModel)]="blog.tags" placeholder="Enter tags separated by commas...">
                <mat-icon matSuffix>label</mat-icon>
              </mat-form-field>

              <!-- Cover Image Upload -->
              <div class="cover-image-section">
                <h3>
                  <mat-icon>image</mat-icon>
                  Cover Image
                </h3>
                <input 
                  type="file" 
                  #coverImageInput
                  (change)="onCoverImageSelected($event)"
                  accept="image/*"
                  style="display: none;">
                <button 
                  mat-raised-button 
                  color="primary"
                  (click)="coverImageInput.click()">
                  <mat-icon>cloud_upload</mat-icon>
                  Choose Cover Image
                </button>
                <div *ngIf="coverImageUrl" class="cover-image-preview">
                  <img [src]="coverImageUrl" alt="Cover image preview" class="cover-image">
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Content Editor Tab -->
          <mat-tab label="Content">
            <div class="tab-content">
              <app-blog-editor
                [initialBlocks]="contentBlocks"
                [readonly]="false"
                (blocksChange)="onBlocksChange($event)">
              </app-blog-editor>
            </div>
          </mat-tab>
        </mat-tab-group>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <button 
            mat-raised-button 
            (click)="cancel()"
            [disabled]="isSubmitting">
            <mat-icon>cancel</mat-icon>
            Cancel
          </button>
          
          <button 
            mat-raised-button 
            color="accent"
            (click)="onPreview()"
            [disabled]="!isFormValid() || isSubmitting">
            <mat-icon>visibility</mat-icon>
            Preview
          </button>
          
          <button 
            mat-raised-button 
            color="primary"
            (click)="onSubmit()"
            [disabled]="!isFormValid() || isSubmitting">
            <mat-icon *ngIf="!isSubmitting">{{ isEditMode ? 'save' : 'publish' }}</mat-icon>
            <mat-icon *ngIf="isSubmitting" class="spinning">hourglass_empty</mat-icon>
            {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Blog' : 'Create Blog') }}
          </button>
        </div>
      </div>

      <!-- Preview Section -->
      <div *ngIf="showPreview" class="preview-section">
        <h2>
          <mat-icon>visibility</mat-icon>
          Blog Preview
        </h2>
        
        <div class="preview-content">
          <app-blog-preview
            [blog]="getBlogForPreview()"
            [blocks]="contentBlocks"
            [coverImageUrl]="coverImageUrl"
            [showMetadata]="false">
          </app-blog-preview>
        </div>
        
        <div class="preview-actions">
          <button
            mat-raised-button
            (click)="cancelPreview()">
            <mat-icon>edit</mat-icon>
            Back to Edit
          </button>

          <button
            mat-raised-button
            color="primary"
            (click)="onSubmit()"
            [disabled]="isSubmitting">
            <mat-icon *ngIf="!isSubmitting">{{ isEditMode ? 'save' : 'publish' }}</mat-icon>
            <mat-icon *ngIf="isSubmitting" class="spinning">hourglass_empty</mat-icon>
            {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Blog' : 'Confirm & Create') }}
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
