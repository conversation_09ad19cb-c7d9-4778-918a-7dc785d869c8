import { ApplicationConfig } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';

import { CalendarModule, DateAdapter } from 'angular-calendar';
import { adapterFactory } from 'angular-calendar/date-adapters/date-fns';

// Material DateAdapter imports
import { DateAdapter as MatDateAdapter } from '@angular/material/core';
import { NativeDateAdapter } from '@angular/material/core';
import { provideNativeDateAdapter } from '@angular/material/core';

import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { AuthInterceptor } from './auth.interceptor';
import { provideHttpClient } from '@angular/common/http';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(),
    provideAnimationsAsync(),
    // Angular Calendar DateAdapter
    ...(CalendarModule.forRoot({ provide: DateAdapter, useFactory: adapterFactory }).providers || []),
    // Material DateAdapter for Material date pickers
    provideNativeDateAdapter(),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true }
  ]
};
