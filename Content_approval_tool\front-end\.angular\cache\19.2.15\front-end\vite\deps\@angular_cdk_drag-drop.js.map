{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/drag-drop.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { signal, Component, ViewEncapsulation, ChangeDetectionStrategy, inject, NgZone, RendererFactory2, Injectable, InjectionToken, ElementRef, booleanAttribute, Directive, Input, ViewContainerRef, ChangeDetectorRef, EventEmitter, Injector, afterNextRender, numberAttribute, Output, TemplateRef, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge, BehaviorSubject } from 'rxjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { a as isFakeTouchstartFromScreenReader, i as isFakeMousedownFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { takeUntil, map, take, tap, switchMap, startWith } from 'rxjs/operators';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { ViewportRuler, ScrollDispatcher, CdkScrollableModule } from './scrolling.mjs';\nexport { CdkScrollable as ɵɵCdkScrollable } from './scrolling.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\nimport './platform-DmdVEw_C.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n\n/** Gets a mutable version of an element's bounding `DOMRect`. */\nfunction getMutableClientRect(element) {\n  const rect = element.getBoundingClientRect();\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `DOMRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    x: rect.x,\n    y: rect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `DOMRect`.\n * @param clientRect DOMRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `DOMRect`, as well as their bottom/right counterparts.\n * @param domRect `DOMRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustDomRect(domRect, top, left) {\n  domRect.top += top;\n  domRect.bottom = domRect.top + domRect.height;\n  domRect.left += left;\n  domRect.right = domRect.left + domRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a DOMRect.\n * @param rect DOMRect to check against.\n * @param threshold Threshold around the DOMRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearDomRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n  _document;\n  /** Cached positions of the scrollable parent elements. */\n  positions = new Map();\n  constructor(_document) {\n    this._document = _document;\n  }\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n    const cachedPosition = this.positions.get(target);\n    if (!cachedPosition) {\n      return null;\n    }\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustDomRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {\n      top: window.scrollY,\n      left: window.scrollX\n    };\n  }\n}\n\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst importantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\nclass PreviewRef {\n  _document;\n  _rootElement;\n  _direction;\n  _initialDomRect;\n  _previewTemplate;\n  _previewClass;\n  _pickupPositionOnPage;\n  _initialTransform;\n  _zIndex;\n  _renderer;\n  /** Reference to the view of the preview element. */\n  _previewEmbeddedView;\n  /** Reference to the preview element. */\n  _preview;\n  get element() {\n    return this._preview;\n  }\n  constructor(_document, _rootElement, _direction, _initialDomRect, _previewTemplate, _previewClass, _pickupPositionOnPage, _initialTransform, _zIndex, _renderer) {\n    this._document = _document;\n    this._rootElement = _rootElement;\n    this._direction = _direction;\n    this._initialDomRect = _initialDomRect;\n    this._previewTemplate = _previewTemplate;\n    this._previewClass = _previewClass;\n    this._pickupPositionOnPage = _pickupPositionOnPage;\n    this._initialTransform = _initialTransform;\n    this._zIndex = _zIndex;\n    this._renderer = _renderer;\n  }\n  attach(parent) {\n    this._preview = this._createPreview();\n    parent.appendChild(this._preview);\n    // The null check is necessary for browsers that don't support the popover API.\n    // Note that we use a string access for compatibility with Closure.\n    if (supportsPopover(this._preview)) {\n      this._preview['showPopover']();\n    }\n  }\n  destroy() {\n    this._preview.remove();\n    this._previewEmbeddedView?.destroy();\n    this._preview = this._previewEmbeddedView = null;\n  }\n  setTransform(value) {\n    this._preview.style.transform = value;\n  }\n  getBoundingClientRect() {\n    return this._preview.getBoundingClientRect();\n  }\n  addClass(className) {\n    this._preview.classList.add(className);\n  }\n  getTransitionDuration() {\n    return getTransformTransitionDurationInMs(this._preview);\n  }\n  addEventListener(name, handler) {\n    return this._renderer.listen(this._preview, name, handler);\n  }\n  _createPreview() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this._previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialDomRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewEmbeddedView = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialDomRect);\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // If the preview has a margin, it can throw off our positioning so we reset it. The reset\n      // value for `margin-right` needs to be `auto` when opened as a popover, because our\n      // positioning is always top/left based, but native popover seems to position itself\n      // to the top/right if `<html>` or `<body>` have `dir=\"rtl\"` (see #29604). Setting it\n      // to `auto` pushed it to the top/left corner in RTL and is a noop in LTR.\n      'margin': supportsPopover(preview) ? '0 auto 0 0' : '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': this._zIndex + ''\n    }, importantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('popover', 'manual');\n    preview.setAttribute('dir', this._direction);\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n    return preview;\n  }\n}\n/** Checks whether a specific element supports the popover API. */\nfunction supportsPopover(element) {\n  return 'showPopover' in element;\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = {\n  passive: true\n};\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = {\n  passive: false\n};\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions$1 = {\n  passive: false,\n  capture: true\n};\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n  _config;\n  _document;\n  _ngZone;\n  _viewportRuler;\n  _dragDropRegistry;\n  _renderer;\n  _rootElementCleanups;\n  _cleanupShadowRootSelectStart;\n  /** Element displayed next to the user's pointer while the element is dragged. */\n  _preview;\n  /** Container into which to insert the preview. */\n  _previewContainer;\n  /** Reference to the view of the placeholder element. */\n  _placeholderRef;\n  /** Element that is rendered instead of the draggable item while it is being sorted. */\n  _placeholder;\n  /** Coordinates within the element at which the user picked up the element. */\n  _pickupPositionInElement;\n  /** Coordinates on the page at which the user picked up the element. */\n  _pickupPositionOnPage;\n  /**\n   * Anchor node used to save the place in the DOM where the element was\n   * picked up so that it can be restored at the end of the drag sequence.\n   */\n  _anchor;\n  /**\n   * CSS `transform` applied to the element when it isn't being dragged. We need a\n   * passive transform in order for the dragged element to retain its new position\n   * after the user has stopped dragging and because we need to know the relative\n   * position in case they start dragging again. This corresponds to `element.style.transform`.\n   */\n  _passiveTransform = {\n    x: 0,\n    y: 0\n  };\n  /** CSS `transform` that is applied to the element while it's being dragged. */\n  _activeTransform = {\n    x: 0,\n    y: 0\n  };\n  /** Inline `transform` value that the element had before the first dragging sequence. */\n  _initialTransform;\n  /**\n   * Whether the dragging sequence has been started. Doesn't\n   * necessarily mean that the element has been moved.\n   */\n  _hasStartedDragging = signal(false);\n  /** Whether the element has moved since the user started dragging it. */\n  _hasMoved;\n  /** Drop container in which the DragRef resided when dragging began. */\n  _initialContainer;\n  /** Index at which the item started in its initial container. */\n  _initialIndex;\n  /** Cached positions of scrollable parent elements. */\n  _parentPositions;\n  /** Emits when the item is being moved. */\n  _moveEvents = new Subject();\n  /** Keeps track of the direction in which the user is dragging along each axis. */\n  _pointerDirectionDelta;\n  /** Pointer position at which the last change in the delta occurred. */\n  _pointerPositionAtLastDirectionChange;\n  /** Position of the pointer at the last pointer event. */\n  _lastKnownPointerPosition;\n  /**\n   * Root DOM node of the drag instance. This is the element that will\n   * be moved around as the user is dragging.\n   */\n  _rootElement;\n  /**\n   * Nearest ancestor SVG, relative to which coordinates are calculated if dragging SVGElement\n   */\n  _ownerSVGElement;\n  /**\n   * Inline style value of `-webkit-tap-highlight-color` at the time the\n   * dragging was started. Used to restore the value once we're done dragging.\n   */\n  _rootElementTapHighlight;\n  /** Subscription to pointer movement events. */\n  _pointerMoveSubscription = Subscription.EMPTY;\n  /** Subscription to the event that is dispatched when the user lifts their pointer. */\n  _pointerUpSubscription = Subscription.EMPTY;\n  /** Subscription to the viewport being scrolled. */\n  _scrollSubscription = Subscription.EMPTY;\n  /** Subscription to the viewport being resized. */\n  _resizeSubscription = Subscription.EMPTY;\n  /**\n   * Time at which the last touch event occurred. Used to avoid firing the same\n   * events multiple times on touch devices where the browser will fire a fake\n   * mouse event for each touch event, after a certain time.\n   */\n  _lastTouchEventTime;\n  /** Time at which the last dragging sequence was started. */\n  _dragStartTime;\n  /** Cached reference to the boundary element. */\n  _boundaryElement = null;\n  /** Whether the native dragging interactions have been enabled on the root element. */\n  _nativeInteractionsEnabled = true;\n  /** Client rect of the root element when the dragging sequence has started. */\n  _initialDomRect;\n  /** Cached dimensions of the preview element. Should be read via `_getPreviewRect`. */\n  _previewRect;\n  /** Cached dimensions of the boundary element. */\n  _boundaryRect;\n  /** Element that will be used as a template to create the draggable item's preview. */\n  _previewTemplate;\n  /** Template for placeholder element rendered to show where a draggable would be dropped. */\n  _placeholderTemplate;\n  /** Elements that can be used to drag the draggable item. */\n  _handles = [];\n  /** Registered handles that are currently disabled. */\n  _disabledHandles = new Set();\n  /** Droppable container that the draggable is a part of. */\n  _dropContainer;\n  /** Layout direction of the item. */\n  _direction = 'ltr';\n  /** Ref that the current drag item is nested in. */\n  _parentDragRef;\n  /**\n   * Cached shadow root that the element is placed in. `null` means that the element isn't in\n   * the shadow DOM and `undefined` means that it hasn't been resolved yet. Should be read via\n   * `_getShadowRoot`, not directly.\n   */\n  _cachedShadowRoot;\n  /** Axis along which dragging is locked. */\n  lockAxis;\n  /**\n   * Amount of milliseconds to wait after the user has put their\n   * pointer down before starting to drag the element.\n   */\n  dragStartDelay = 0;\n  /** Class to be added to the preview element. */\n  previewClass;\n  /**\n   * If the parent of the dragged element has a `scale` transform, it can throw off the\n   * positioning when the user starts dragging. Use this input to notify the CDK of the scale.\n   */\n  scale = 1;\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, value));\n    }\n  }\n  _disabled = false;\n  /** Emits as the drag sequence is being prepared. */\n  beforeStarted = new Subject();\n  /** Emits when the user starts dragging the item. */\n  started = new Subject();\n  /** Emits when the user has released a drag item, before any animations have started. */\n  released = new Subject();\n  /** Emits when the user stops dragging an item in the container. */\n  ended = new Subject();\n  /** Emits when the user has moved the item into a new container. */\n  entered = new Subject();\n  /** Emits when the user removes the item its container by dragging it into another container. */\n  exited = new Subject();\n  /** Emits when the user drops the item inside a container. */\n  dropped = new Subject();\n  /**\n   * Emits as the user is dragging the item. Use with caution,\n   * because this event will fire for every pixel that the user has dragged.\n   */\n  moved = this._moveEvents;\n  /** Arbitrary data that can be attached to the drag item. */\n  data;\n  /**\n   * Function that can be used to customize the logic of how the position of the drag item\n   * is limited while it's being dragged. Gets called with a point containing the current position\n   * of the user's pointer on the page, a reference to the item being dragged and its dimensions.\n   * Should return a point describing where the item should be rendered.\n   */\n  constrainPosition;\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry, _renderer) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    this._renderer = _renderer;\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n    if (element !== this._rootElement) {\n      this._removeRootElementListeners();\n      this._rootElementCleanups = this._ngZone.runOutsideAngular(() => [_bindEventWithOptions(this._renderer, element, 'mousedown', this._pointerDown, activeEventListenerOptions), _bindEventWithOptions(this._renderer, element, 'touchstart', this._pointerDown, passiveEventListenerOptions), _bindEventWithOptions(this._renderer, element, 'dragstart', this._nativeDragStart, activeEventListenerOptions)]);\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners();\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n    this._anchor?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeListeners();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n  isDragging() {\n    return this._hasStartedDragging() && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n  _removeListeners() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n    this._cleanupShadowRootSelectStart?.();\n    this._cleanupShadowRootSelectStart = undefined;\n  }\n  /** Destroys the preview element and its ViewRef. */\n  _destroyPreview() {\n    this._preview?.destroy();\n    this._preview = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n  _destroyPlaceholder() {\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /** Handler for the `mousedown`/`touchstart` events. */\n  _pointerDown = event => {\n    this.beforeStarted.next();\n    // Delegate the event based on whether it started from a handle or the element itself.\n    if (this._handles.length) {\n      const targetHandle = this._getTargetHandle(event);\n      if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n        this._initializeDragSequence(targetHandle, event);\n      }\n    } else if (!this.disabled) {\n      this._initializeDragSequence(this._rootElement, event);\n    }\n  };\n  /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n  _pointerMove = event => {\n    const pointerPosition = this._getPointerPositionOnPage(event);\n    if (!this._hasStartedDragging()) {\n      const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n      const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n      const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n      // Only start dragging after the user has moved more than the minimum distance in either\n      // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n      // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n      // per pixel of movement (e.g. if the user moves their pointer quickly).\n      if (isOverThreshold) {\n        const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n        const container = this._dropContainer;\n        if (!isDelayElapsed) {\n          this._endDragSequence(event);\n          return;\n        }\n        // Prevent other drag sequences from starting while something in the container is still\n        // being dragged. This can happen while we're waiting for the drop animation to finish\n        // and can cause errors, because some elements might still be moving around.\n        if (!container || !container.isDragging() && !container.isReceiving()) {\n          // Prevent the default action as soon as the dragging sequence is considered as\n          // \"started\" since waiting for the next event can allow the device to begin scrolling.\n          if (event.cancelable) {\n            event.preventDefault();\n          }\n          this._hasStartedDragging.set(true);\n          this._ngZone.run(() => this._startDragSequence(event));\n        }\n      }\n      return;\n    }\n    // We prevent the default action down here so that we know that dragging has started. This is\n    // important for touch devices where doing this too early can unnecessarily block scrolling,\n    // if there's a dragging delay.\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n    this._hasMoved = true;\n    this._lastKnownPointerPosition = pointerPosition;\n    this._updatePointerDirectionDelta(constrainedPointerPosition);\n    if (this._dropContainer) {\n      this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n    } else {\n      // If there's a position constraint function, we want the element's top/left to be at the\n      // specific position on the page. Use the initial position as a reference if that's the case.\n      const offset = this.constrainPosition ? this._initialDomRect : this._pickupPositionOnPage;\n      const activeTransform = this._activeTransform;\n      activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n      activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n      this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n    }\n    // Since this event gets fired for every pixel while dragging, we only\n    // want to fire it if the consumer opted into it. Also we have to\n    // re-enter the zone because we run all of the events on the outside.\n    if (this._moveEvents.observers.length) {\n      this._ngZone.run(() => {\n        this._moveEvents.next({\n          source: this,\n          pointerPosition: constrainedPointerPosition,\n          event,\n          distance: this._getDragDistance(constrainedPointerPosition),\n          delta: this._pointerDirectionDelta\n        });\n      });\n    }\n  };\n  /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n  _pointerUp = event => {\n    this._endDragSequence(event);\n  };\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n    this._removeListeners();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n    if (!this._hasStartedDragging()) {\n      return;\n    }\n    this.released.next({\n      source: this,\n      event\n    });\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n    this._toggleNativeDragInteractions();\n    // Needs to happen before the root element is moved.\n    const shadowRoot = this._getShadowRoot();\n    const dropContainer = this._dropContainer;\n    if (shadowRoot) {\n      // In some browsers the global `selectstart` that we maintain in the `DragDropRegistry`\n      // doesn't cross the shadow boundary so we have to prevent it at the shadow root (see #28792).\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupShadowRootSelectStart = _bindEventWithOptions(this._renderer, shadowRoot, 'selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n      });\n    }\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n      const anchor = this._anchor = this._anchor || this._document.createComment(typeof ngDevMode === 'undefined' || ngDevMode ? 'cdk-drag-anchor' : '');\n      // Insert an anchor node so that we can restore the element's position in the DOM.\n      parent.insertBefore(anchor, element);\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = new PreviewRef(this._document, this._rootElement, this._direction, this._initialDomRect, this._previewTemplate || null, this.previewClass || null, this._pickupPositionOnPage, this._initialTransform, this._config.zIndex || 1000, this._renderer);\n      this._preview.attach(this._getPreviewInsertionPoint(parent, shadowRoot));\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this.started.next({\n        source: this,\n        event\n      }); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this,\n        event\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    }\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event);\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n    this._hasMoved = false;\n    this._hasStartedDragging.set(this._hasMoved);\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeListeners();\n    this._initialDomRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(this._initialDomRect, referenceElement, event);\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialDomRect = this._boundaryRect = this._previewRect = this._initialTransform = undefined;\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n        this._dropContainer.exit(this);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer;\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n        // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    }\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n      }\n    }\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n    // Apply the class that adds a transition to the preview.\n    this._preview.addClass('cdk-drag-animating');\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = this._preview.getTransitionDuration();\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          if (!event || this._preview && _getEventTarget(event) === this._preview.element && event.propertyName === 'transform') {\n            cleanupListener();\n            resolve();\n            clearTimeout(timeout);\n          }\n        };\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler, duration * 1.5);\n        const cleanupListener = this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  _getPointerPositionInElement(elementRect, referenceElement, event) {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event) ?\n    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this, this._initialDomRect, this._pickupPositionInElement) : point;\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y - (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x - (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n    }\n    if (this._boundaryRect) {\n      // If not using a custom constrain we need to account for the pickup position in the element\n      // otherwise we do not need to do this, as it has already been accounted for\n      const {\n        x: pickupX,\n        y: pickupY\n      } = !this.constrainPosition ? this._pickupPositionInElement : {\n        x: 0,\n        y: 0\n      };\n      const boundaryRect = this._boundaryRect;\n      const {\n        width: previewWidth,\n        height: previewHeight\n      } = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n  _removeRootElementListeners() {\n    this._rootElementCleanups?.forEach(cleanup => cleanup());\n    this._rootElementCleanups = undefined;\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyRootElementTransform(x, y) {\n    const scale = 1 / this.scale;\n    const transform = getTransform(x * scale, y * scale);\n    const styles = this._rootElement.style;\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyPreviewTransform(x, y) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.setTransform(combineTransforms(transform, initialTransform));\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n    if (scrollDifference) {\n      const target = _getEventTarget(event);\n      // DOMRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary DOMRect if the user has scrolled.\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustDomRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n  _getViewportScrollPosition() {\n    return this._parentPositions.positions.get(this._document)?.scrollPosition || this._parentPositions.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n    return coerceElement(previewContainer);\n  }\n  /** Lazily resolves and returns the dimensions of the preview. */\n  _getPreviewRect() {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n      this._previewRect = this._preview ? this._preview.getBoundingClientRect() : this._initialDomRect;\n    }\n    return this._previewRect;\n  }\n  /** Handles a native `dragstart` event. */\n  _nativeDragStart = event => {\n    if (this._handles.length) {\n      const targetHandle = this._getTargetHandle(event);\n      if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n        event.preventDefault();\n      }\n    } else if (!this.disabled) {\n      // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n      // but some cases like dragging of links can slip through (see #24403).\n      event.preventDefault();\n    }\n  };\n  /** Gets a handle that is the target of an event. */\n  _getTargetHandle(event) {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target));\n    });\n  }\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/** Callback invoked for `selectstart` events inside the shadow DOM. */\nfunction shadowDomSelectStart(event) {\n  event.preventDefault();\n}\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n  if (from === to) {\n    return;\n  }\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n  _dragDropRegistry;\n  /** Root element container of the drop list. */\n  _element;\n  /** Function used to determine if an item can be sorted into a specific index. */\n  _sortPredicate;\n  /** Cache of the dimensions of all the items inside the container. */\n  _itemPositions = [];\n  /**\n   * Draggable items that are currently active inside the container. Includes the items\n   * that were there at the start of the sequence, as well as any items that have been dragged\n   * in, but haven't been dropped yet.\n   */\n  _activeDraggables;\n  /** Direction in which the list is oriented. */\n  orientation = 'vertical';\n  /** Layout direction of the drop list. */\n  direction;\n  constructor(_dragDropRegistry) {\n    this._dragDropRegistry = _dragDropRegistry;\n  }\n  /**\n   * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n   * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n   * overlap with the swapped item after the swapping occurred.\n   */\n  _previousSwap = {\n    drag: null,\n    delta: 0,\n    overlaps: false\n  };\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement();\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n      const transformAmount = Math.round(sibling.offset * (1 / sibling.drag.scale));\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${transformAmount}px, 0, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${transformAmount}px, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, offset, 0);\n      }\n    });\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n    return {\n      previousIndex: currentIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    const newIndex = index == null || index < 0 ?\n    // We use the coordinates of where the item entered the drop\n    // zone to figure out at which index it should be inserted.\n    this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex];\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    }\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      this._element.appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables?.forEach(item => {\n      const rootElement = item.getRootElement();\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeDraggables;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items = this.orientation === 'horizontal' && this.direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference, leftDifference) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({\n      clientRect\n    }) => {\n      adjustDomRect(clientRect, topDifference, leftDifference);\n    });\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({\n      drag\n    }) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n  withElementContainer(container) {\n    this._element = container;\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n  _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top;\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n    return itemOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n    return siblingOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n      return isHorizontal ?\n      // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n\n/**\n * Strategy that only supports sorting on a list that might wrap.\n * Items are reordered by moving their DOM nodes around.\n * @docs-private\n */\nclass MixedSortStrategy {\n  _document;\n  _dragDropRegistry;\n  /** Root element container of the drop list. */\n  _element;\n  /** Function used to determine if an item can be sorted into a specific index. */\n  _sortPredicate;\n  /** Lazily-resolved root node containing the list. Use `_getRootNode` to read this. */\n  _rootNode;\n  /**\n   * Draggable items that are currently active inside the container. Includes the items\n   * that were there at the start of the sequence, as well as any items that have been dragged\n   * in, but haven't been dropped yet.\n   */\n  _activeItems;\n  /**\n   * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n   * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n   * overlap with the swapped item after the swapping occurred.\n   */\n  _previousSwap = {\n    drag: null,\n    deltaX: 0,\n    deltaY: 0,\n    overlaps: false\n  };\n  /**\n   * Keeps track of the relationship between a node and its next sibling. This information\n   * is used to restore the DOM to the order it was in before dragging started.\n   */\n  _relatedNodes = [];\n  constructor(_document, _dragDropRegistry) {\n    this._document = _document;\n    this._dragDropRegistry = _dragDropRegistry;\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    const childNodes = this._element.childNodes;\n    this._relatedNodes = [];\n    for (let i = 0; i < childNodes.length; i++) {\n      const node = childNodes[i];\n      this._relatedNodes.push([node, node.nextSibling]);\n    }\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY);\n    const previousSwap = this._previousSwap;\n    if (newIndex === -1 || this._activeItems[newIndex] === item) {\n      return null;\n    }\n    const toSwapWith = this._activeItems[newIndex];\n    // Prevent too many swaps over the same item.\n    if (previousSwap.drag === toSwapWith && previousSwap.overlaps && previousSwap.deltaX === pointerDelta.x && previousSwap.deltaY === pointerDelta.y) {\n      return null;\n    }\n    const previousIndex = this.getItemIndex(item);\n    const current = item.getPlaceholderElement();\n    const overlapElement = toSwapWith.getRootElement();\n    if (newIndex > previousIndex) {\n      overlapElement.after(current);\n    } else {\n      overlapElement.before(current);\n    }\n    moveItemInArray(this._activeItems, previousIndex, newIndex);\n    const newOverlapElement = this._getRootNode().elementFromPoint(pointerX, pointerY);\n    // Note: it's tempting to save the entire `pointerDelta` object here, however that'll\n    // break this functionality, because the same object is passed for all `sort` calls.\n    previousSwap.deltaX = pointerDelta.x;\n    previousSwap.deltaY = pointerDelta.y;\n    previousSwap.drag = toSwapWith;\n    previousSwap.overlaps = overlapElement === newOverlapElement || overlapElement.contains(newOverlapElement);\n    return {\n      previousIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    let enterIndex = index == null || index < 0 ? this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    // In some cases (e.g. when the container has padding) we might not be able to figure\n    // out which item to insert the dragged item next to, because the pointer didn't overlap\n    // with anything. In that case we find the item that's closest to the pointer.\n    if (enterIndex === -1) {\n      enterIndex = this._getClosestItemIndexToPointer(item, pointerX, pointerY);\n    }\n    const targetItem = this._activeItems[enterIndex];\n    const currentIndex = this._activeItems.indexOf(item);\n    if (currentIndex > -1) {\n      this._activeItems.splice(currentIndex, 1);\n    }\n    if (targetItem && !this._dragDropRegistry.isDragging(targetItem)) {\n      this._activeItems.splice(enterIndex, 0, item);\n      targetItem.getRootElement().before(item.getPlaceholderElement());\n    } else {\n      this._activeItems.push(item);\n      this._element.appendChild(item.getPlaceholderElement());\n    }\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeItems = items.slice();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    const root = this._element;\n    const previousSwap = this._previousSwap;\n    // Moving elements around in the DOM can break things like the `@for` loop, because it\n    // uses comment nodes to know where to insert elements. To avoid such issues, we restore\n    // the DOM nodes in the list to their original order when the list is reset.\n    // Note that this could be simpler if we just saved all the nodes, cleared the root\n    // and then appended them in the original order. We don't do it, because it can break\n    // down depending on when the snapshot was taken. E.g. we may end up snapshotting the\n    // placeholder element which is removed after dragging.\n    for (let i = this._relatedNodes.length - 1; i > -1; i--) {\n      const [node, nextSibling] = this._relatedNodes[i];\n      if (node.parentNode === root && node.nextSibling !== nextSibling) {\n        if (nextSibling === null) {\n          root.appendChild(node);\n        } else if (nextSibling.parentNode === root) {\n          root.insertBefore(node, nextSibling);\n        }\n      }\n    }\n    this._relatedNodes = [];\n    this._activeItems = [];\n    previousSwap.drag = null;\n    previousSwap.deltaX = previousSwap.deltaY = 0;\n    previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeItems;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    return this._activeItems.indexOf(item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll() {\n    this._activeItems.forEach(item => {\n      if (this._dragDropRegistry.isDragging(item)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        item._sortFromLastPointerPosition();\n      }\n    });\n  }\n  withElementContainer(container) {\n    if (container !== this._element) {\n      this._element = container;\n      this._rootNode = undefined;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY) {\n    const elementAtPoint = this._getRootNode().elementFromPoint(Math.floor(pointerX), Math.floor(pointerY));\n    const index = elementAtPoint ? this._activeItems.findIndex(item => {\n      const root = item.getRootElement();\n      return elementAtPoint === root || root.contains(elementAtPoint);\n    }) : -1;\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n  /** Lazily resolves the list's root node. */\n  _getRootNode() {\n    // Resolve the root node lazily to ensure that the drop list is in its final place in the DOM.\n    if (!this._rootNode) {\n      this._rootNode = _getShadowRoot(this._element) || this._document;\n    }\n    return this._rootNode;\n  }\n  /**\n   * Finds the index of the item that's closest to the item being dragged.\n   * @param item Item being dragged.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _getClosestItemIndexToPointer(item, pointerX, pointerY) {\n    if (this._activeItems.length === 0) {\n      return -1;\n    }\n    if (this._activeItems.length === 1) {\n      return 0;\n    }\n    let minDistance = Infinity;\n    let minIndex = -1;\n    // Find the Euclidean distance (https://en.wikipedia.org/wiki/Euclidean_distance) between each\n    // item and the pointer, and return the smallest one. Note that this is a bit flawed in that DOM\n    // nodes are rectangles, not points, so we use the top/left coordinates. It should be enough\n    // for our purposes.\n    for (let i = 0; i < this._activeItems.length; i++) {\n      const current = this._activeItems[i];\n      if (current !== item) {\n        const {\n          x,\n          y\n        } = current.getRootElement().getBoundingClientRect();\n        const distance = Math.hypot(pointerX - x, pointerY - y);\n        if (distance < minDistance) {\n          minDistance = distance;\n          minIndex = i;\n        }\n      }\n    }\n    return minIndex;\n  }\n}\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/** Vertical direction in which we can auto-scroll. */\nvar AutoScrollVerticalDirection;\n(function (AutoScrollVerticalDirection) {\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"UP\"] = 1] = \"UP\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"DOWN\"] = 2] = \"DOWN\";\n})(AutoScrollVerticalDirection || (AutoScrollVerticalDirection = {}));\n/** Horizontal direction in which we can auto-scroll. */\nvar AutoScrollHorizontalDirection;\n(function (AutoScrollHorizontalDirection) {\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"LEFT\"] = 1] = \"LEFT\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"RIGHT\"] = 2] = \"RIGHT\";\n})(AutoScrollHorizontalDirection || (AutoScrollHorizontalDirection = {}));\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n  _dragDropRegistry;\n  _ngZone;\n  _viewportRuler;\n  /** Element that the drop list is attached to. */\n  element;\n  /** Whether starting a dragging sequence from this container is disabled. */\n  disabled = false;\n  /** Whether sorting items within the list is disabled. */\n  sortingDisabled = false;\n  /** Locks the position of the draggable elements inside the container along the specified axis. */\n  lockAxis;\n  /**\n   * Whether auto-scrolling the view when the user\n   * moves their pointer close to the edges is disabled.\n   */\n  autoScrollDisabled = false;\n  /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n  autoScrollStep = 2;\n  /**\n   * Function that is used to determine whether an item\n   * is allowed to be moved into a drop container.\n   */\n  enterPredicate = () => true;\n  /** Function that is used to determine whether an item can be sorted into a particular index. */\n  sortPredicate = () => true;\n  /** Emits right before dragging has started. */\n  beforeStarted = new Subject();\n  /**\n   * Emits when the user has moved a new drag item into this container.\n   */\n  entered = new Subject();\n  /**\n   * Emits when the user removes an item from the container\n   * by dragging it into another container.\n   */\n  exited = new Subject();\n  /** Emits when the user drops an item inside the container. */\n  dropped = new Subject();\n  /** Emits as the user is swapping items while actively dragging. */\n  sorted = new Subject();\n  /** Emits when a dragging sequence is started in a list connected to the current one. */\n  receivingStarted = new Subject();\n  /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n  receivingStopped = new Subject();\n  /** Arbitrary data that can be attached to the drop list. */\n  data;\n  /** Element that is the direct parent of the drag items. */\n  _container;\n  /** Whether an item in the list is being dragged. */\n  _isDragging = false;\n  /** Keeps track of the positions of any parent scrollable elements. */\n  _parentPositions;\n  /** Strategy being used to sort items within the list. */\n  _sortStrategy;\n  /** Cached `DOMRect` of the drop list. */\n  _domRect;\n  /** Draggable items in the container. */\n  _draggables = [];\n  /** Drop lists that are connected to the current one. */\n  _siblings = [];\n  /** Connected siblings that currently have a dragged item. */\n  _activeSiblings = new Set();\n  /** Subscription to the window being scrolled. */\n  _viewportScrollSubscription = Subscription.EMPTY;\n  /** Vertical direction in which the list is currently scrolling. */\n  _verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n  /** Horizontal direction in which the list is currently scrolling. */\n  _horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n  /** Node that is being auto-scrolled. */\n  _scrollNode;\n  /** Used to signal to the current auto-scroll sequence when to stop. */\n  _stopScrollTimers = new Subject();\n  /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n  _cachedShadowRoot = null;\n  /** Reference to the document. */\n  _document;\n  /** Elements that can be scrolled while the user is dragging. */\n  _scrollableElements = [];\n  /** Initial value for the element's `scroll-snap-type` style. */\n  _initialScrollSnap;\n  /** Direction of the list's layout. */\n  _direction = 'ltr';\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    const coercedElement = this.element = coerceElement(element);\n    this._document = _document;\n    this.withOrientation('vertical').withElementContainer(coercedElement);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n  }\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n  start() {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted();\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item) {\n    this._reset();\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction) {\n    this._direction = direction;\n    if (this._sortStrategy instanceof SingleAxisSortStrategy) {\n      this._sortStrategy.direction = direction;\n    }\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation) {\n    if (orientation === 'mixed') {\n      this._sortStrategy = new MixedSortStrategy(this._document, this._dragDropRegistry);\n    } else {\n      const strategy = new SingleAxisSortStrategy(this._dragDropRegistry);\n      strategy.direction = this._direction;\n      strategy.orientation = orientation;\n      this._sortStrategy = strategy;\n    }\n    this._sortStrategy.withElementContainer(this._container);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements) {\n    const element = this._container;\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /**\n   * Configures the drop list so that a different element is used as the container for the\n   * dragged items. This is useful for the cases when one might not have control over the\n   * full DOM that sets up the dragging.\n   * Note that the alternate container needs to be a descendant of the drop list.\n   * @param container New element container to be assigned.\n   */\n  withElementContainer(container) {\n    if (container === this._container) {\n      return this;\n    }\n    const element = coerceElement(this.element);\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && container !== element && !element.contains(container)) {\n      throw new Error('Invalid DOM structure for drop list. Alternate container element must be a descendant of the drop list.');\n    }\n    const oldContainerIndex = this._scrollableElements.indexOf(this._container);\n    const newContainerIndex = this._scrollableElements.indexOf(container);\n    if (oldContainerIndex > -1) {\n      this._scrollableElements.splice(oldContainerIndex, 1);\n    }\n    if (newContainerIndex > -1) {\n      this._scrollableElements.splice(newContainerIndex, 1);\n    }\n    if (this._sortStrategy) {\n      this._sortStrategy.withElementContainer(container);\n    }\n    this._cachedShadowRoot = null;\n    this._scrollableElements.unshift(container);\n    this._container = container;\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item) {\n    return this._isDragging ? this._sortStrategy.getItemIndex(item) : this._draggables.indexOf(item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._domRect || !isPointerNearDomRect(this._domRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item\n      });\n    }\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n    let scrollNode;\n    let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n      if (isPointerNearDomRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, this._direction, pointerX, pointerY);\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    });\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n      const domRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(domRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(domRect, pointerX);\n      scrollNode = window;\n    }\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n  _draggingStarted() {\n    const styles = this._container.style;\n    this.beforeStarted.next();\n    this._isDragging = true;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n    // Prevent the check from running on apps not using an alternate container. Ideally we\n    // would always run it, but introducing it at this stage would be a breaking change.\n    this._container !== coerceElement(this.element)) {\n      for (const drag of this._draggables) {\n        if (!drag.isDragging() && drag.getVisibleElement().parentNode !== this._container) {\n          throw new Error('Invalid DOM structure for drop list. All items must be placed directly inside of the element container.');\n        }\n      }\n    }\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n  _cacheParentPositions() {\n    this._parentPositions.cache(this._scrollableElements);\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `DOMRect`.\n    this._domRect = this._parentPositions.positions.get(this._container).clientRect;\n  }\n  /** Resets the container to its initial state. */\n  _reset() {\n    this._isDragging = false;\n    const styles = this._container.style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n  /** Starts the interval that'll auto-scroll the element. */\n  _startScrollInterval = () => {\n    this._stopScrolling();\n    interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n      const node = this._scrollNode;\n      const scrollStep = this.autoScrollStep;\n      if (this._verticalScrollDirection === AutoScrollVerticalDirection.UP) {\n        node.scrollBy(0, -scrollStep);\n      } else if (this._verticalScrollDirection === AutoScrollVerticalDirection.DOWN) {\n        node.scrollBy(0, scrollStep);\n      }\n      if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.LEFT) {\n        node.scrollBy(-scrollStep, 0);\n      } else if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.RIGHT) {\n        node.scrollBy(scrollStep, 0);\n      }\n    });\n  };\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x, y) {\n    return this._domRect != null && isInsideClientRect(this._domRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item, x, y) {\n    if (!this._domRect || !isInsideClientRect(this._domRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n    // The `DOMRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === this._container || this._container.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items\n      });\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({\n      initiator: sibling,\n      receiver: this\n    });\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n          this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(this._container);\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n  _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy.getActiveItemsSnapshot().filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return AutoScrollVerticalDirection.UP;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return AutoScrollVerticalDirection.DOWN;\n  }\n  return AutoScrollVerticalDirection.NONE;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return AutoScrollHorizontalDirection.LEFT;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return AutoScrollHorizontalDirection.RIGHT;\n  }\n  return AutoScrollHorizontalDirection.NONE;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param direction Layout direction of the drop list.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, direction, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n  let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n    if (computedVertical === AutoScrollVerticalDirection.UP) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = AutoScrollVerticalDirection.UP;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = AutoScrollVerticalDirection.DOWN;\n    }\n  }\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n    if (direction === 'rtl') {\n      if (computedHorizontal === AutoScrollHorizontalDirection.RIGHT) {\n        // In RTL `scrollLeft` will be negative when scrolled.\n        if (scrollLeft < 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n        }\n      } else if (element.scrollWidth + scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n      }\n    } else {\n      if (computedHorizontal === AutoScrollHorizontalDirection.LEFT) {\n        if (scrollLeft > 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n        }\n      } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n      }\n    }\n  }\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/** Event options that can be used to bind a capturing event. */\nconst capturingEventOptions = {\n  capture: true\n};\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = {\n  passive: false,\n  capture: true\n};\n/**\n * Component used to load the drag&drop reset styles.\n * @docs-private\n */\nclass _ResetsLoader {\n  static ɵfac = function _ResetsLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _ResetsLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _ResetsLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"cdk-drag-resets-container\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _ResetsLoader_Template(rf, ctx) {},\n    styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit;inset:auto}}.cdk-drag-placeholder *,.cdk-drag-preview *{pointer-events:none !important}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_ResetsLoader, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'cdk-drag-resets-container': ''\n      },\n      styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit;inset:auto}}.cdk-drag-placeholder *,.cdk-drag-preview *{pointer-events:none !important}\\n\"]\n    }]\n  }], null, null);\n})();\n// TODO(crisbeto): remove generics when making breaking changes.\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\nclass DragDropRegistry {\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cleanupDocumentTouchmove;\n  /** Registered drop container instances. */\n  _dropInstances = new Set();\n  /** Registered drag item instances. */\n  _dragInstances = new Set();\n  /** Drag item instances that are currently being dragged. */\n  _activeDragInstances = signal([]);\n  /** Keeps track of the event listeners that we've bound to the `document`. */\n  _globalListeners;\n  /**\n   * Predicate function to check if an item is being dragged.  Moved out into a property,\n   * because it'll be called a lot and we don't want to create a new function every time.\n   */\n  _draggingPredicate = item => item.isDragging();\n  /**\n   * Map tracking DOM nodes and their corresponding drag directives. Note that this is different\n   * from looking through the `_dragInstances` and getting their root node, because the root node\n   * isn't necessarily the node that the directive is set on.\n   */\n  _domNodesToDirectives = null;\n  /**\n   * Emits the `touchmove` or `mousemove` events that are dispatched\n   * while the user is dragging a drag item instance.\n   */\n  pointerMove = new Subject();\n  /**\n   * Emits the `touchend` or `mouseup` events that are dispatched\n   * while the user is dragging a drag item instance.\n   */\n  pointerUp = new Subject();\n  /**\n   * Emits when the viewport has been scrolled while the user is dragging an item.\n   * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n   * @breaking-change 13.0.0\n   */\n  scroll = new Subject();\n  constructor() {}\n  /** Adds a drop container to the registry. */\n  registerDropContainer(drop) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n  /** Adds a drag item instance to the registry. */\n  registerDragItem(drag) {\n    this._dragInstances.add(drag);\n    // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._cleanupDocumentTouchmove?.();\n        this._cleanupDocumentTouchmove = _bindEventWithOptions(this._renderer, this._document, 'touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      });\n    }\n  }\n  /** Removes a drop container from the registry. */\n  removeDropContainer(drop) {\n    this._dropInstances.delete(drop);\n  }\n  /** Removes a drag item instance from the registry. */\n  removeDragItem(drag) {\n    this._dragInstances.delete(drag);\n    this.stopDragging(drag);\n    if (this._dragInstances.size === 0) {\n      this._cleanupDocumentTouchmove?.();\n    }\n  }\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n  startDragging(drag, event) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances().indexOf(drag) > -1) {\n      return;\n    }\n    this._styleLoader.load(_ResetsLoader);\n    this._activeDragInstances.update(instances => [...instances, drag]);\n    if (this._activeDragInstances().length === 1) {\n      // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n      const isTouchEvent = event.type.startsWith('touch');\n      const endEventHandler = e => this.pointerUp.next(e);\n      const toBind = [\n      // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n      // the document. See https://github.com/angular/components/issues/17144.\n      ['scroll', e => this.scroll.next(e), capturingEventOptions],\n      // Preventing the default action on `mousemove` isn't enough to disable text selection\n      // on Safari so we need to prevent the selection event as well. Alternatively this can\n      // be done by setting `user-select: none` on the `body`, however it has causes a style\n      // recalculation which can be expensive on pages with a lot of elements.\n      ['selectstart', this._preventDefaultWhileDragging, activeCapturingEventOptions]];\n      if (isTouchEvent) {\n        toBind.push(['touchend', endEventHandler, capturingEventOptions], ['touchcancel', endEventHandler, capturingEventOptions]);\n      } else {\n        toBind.push(['mouseup', endEventHandler, capturingEventOptions]);\n      }\n      // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n      if (!isTouchEvent) {\n        toBind.push(['mousemove', e => this.pointerMove.next(e), activeCapturingEventOptions]);\n      }\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners = toBind.map(([name, handler, options]) => _bindEventWithOptions(this._renderer, this._document, name, handler, options));\n      });\n    }\n  }\n  /** Stops dragging a drag item instance. */\n  stopDragging(drag) {\n    this._activeDragInstances.update(instances => {\n      const index = instances.indexOf(drag);\n      if (index > -1) {\n        instances.splice(index, 1);\n        return [...instances];\n      }\n      return instances;\n    });\n    if (this._activeDragInstances().length === 0) {\n      this._clearGlobalListeners();\n    }\n  }\n  /** Gets whether a drag item instance is currently being dragged. */\n  isDragging(drag) {\n    return this._activeDragInstances().indexOf(drag) > -1;\n  }\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n  scrolled(shadowRoot) {\n    const streams = [this.scroll];\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(new Observable(observer => {\n        return this._ngZone.runOutsideAngular(() => {\n          const cleanup = _bindEventWithOptions(this._renderer, shadowRoot, 'scroll', event => {\n            if (this._activeDragInstances().length) {\n              observer.next(event);\n            }\n          }, capturingEventOptions);\n          return () => {\n            cleanup();\n          };\n        });\n      }));\n    }\n    return merge(...streams);\n  }\n  /**\n   * Tracks the DOM node which has a draggable directive.\n   * @param node Node to track.\n   * @param dragRef Drag directive set on the node.\n   */\n  registerDirectiveNode(node, dragRef) {\n    this._domNodesToDirectives ??= new WeakMap();\n    this._domNodesToDirectives.set(node, dragRef);\n  }\n  /**\n   * Stops tracking a draggable directive node.\n   * @param node Node to stop tracking.\n   */\n  removeDirectiveNode(node) {\n    this._domNodesToDirectives?.delete(node);\n  }\n  /**\n   * Gets the drag directive corresponding to a specific DOM node, if any.\n   * @param node Node for which to do the lookup.\n   */\n  getDragDirectiveForNode(node) {\n    return this._domNodesToDirectives?.get(node) || null;\n  }\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n    this._domNodesToDirectives = null;\n    this._clearGlobalListeners();\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n  /**\n   * Event listener that will prevent the default browser action while the user is dragging.\n   * @param event Event whose default action should be prevented.\n   */\n  _preventDefaultWhileDragging = event => {\n    if (this._activeDragInstances().length > 0) {\n      event.preventDefault();\n    }\n  };\n  /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n  _persistentTouchmoveListener = event => {\n    if (this._activeDragInstances().length > 0) {\n      // Note that we only want to prevent the default action after dragging has actually started.\n      // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n      // but it could be pushed back if the user has set up a drag delay or threshold.\n      if (this._activeDragInstances().some(this._draggingPredicate)) {\n        event.preventDefault();\n      }\n      this.pointerMove.next(event);\n    }\n  };\n  /** Clears out the global event listeners from the `document`. */\n  _clearGlobalListeners() {\n    this._globalListeners?.forEach(cleanup => cleanup());\n    this._globalListeners = undefined;\n  }\n  static ɵfac = function DragDropRegistry_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DragDropRegistry)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DragDropRegistry,\n    factory: DragDropRegistry.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n  _document = inject(DOCUMENT);\n  _ngZone = inject(NgZone);\n  _viewportRuler = inject(ViewportRuler);\n  _dragDropRegistry = inject(DragDropRegistry);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  constructor() {}\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n  createDrag(element, config = DEFAULT_CONFIG) {\n    return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry, this._renderer);\n  }\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n  createDropList(element) {\n    return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n  }\n  static ɵfac = function DragDrop_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DragDrop)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DragDrop,\n    factory: DragDrop.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDrop, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n  element = inject(ElementRef);\n  _parentDrag = inject(CDK_DRAG_PARENT, {\n    optional: true,\n    skipSelf: true\n  });\n  _dragDropRegistry = inject(DragDropRegistry);\n  /** Emits when the state of the handle has changed. */\n  _stateChanges = new Subject();\n  /** Whether starting to drag through this handle is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._stateChanges.next(this);\n  }\n  _disabled = false;\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(this.element.nativeElement, 'cdkDragHandle');\n    }\n    this._parentDrag?._addHandle(this);\n  }\n  ngAfterViewInit() {\n    if (!this._parentDrag) {\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        const ref = this._dragDropRegistry.getDragDirectiveForNode(parent);\n        if (ref) {\n          this._parentDrag = ref;\n          ref._addHandle(this);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._parentDrag?._removeHandle(this);\n    this._stateChanges.complete();\n  }\n  static ɵfac = function CdkDragHandle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDragHandle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDragHandle,\n    selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n    hostAttrs: [1, \"cdk-drag-handle\"],\n    inputs: {\n      disabled: [2, \"cdkDragHandleDisabled\", \"disabled\", booleanAttribute]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_HANDLE,\n      useExisting: CdkDragHandle\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragHandle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDragHandle]',\n      host: {\n        'class': 'cdk-drag-handle'\n      },\n      providers: [{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragHandleDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\n\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n  element = inject(ElementRef);\n  dropContainer = inject(CDK_DROP_LIST, {\n    optional: true,\n    skipSelf: true\n  });\n  _ngZone = inject(NgZone);\n  _viewContainerRef = inject(ViewContainerRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _selfHandle = inject(CDK_DRAG_HANDLE, {\n    optional: true,\n    self: true\n  });\n  _parentDrag = inject(CDK_DRAG_PARENT, {\n    optional: true,\n    skipSelf: true\n  });\n  _dragDropRegistry = inject(DragDropRegistry);\n  _destroyed = new Subject();\n  _handles = new BehaviorSubject([]);\n  _previewTemplate;\n  _placeholderTemplate;\n  /** Reference to the underlying drag instance. */\n  _dragRef;\n  /** Arbitrary data to attach to this drag instance. */\n  data;\n  /** Locks the position of the dragged element along the specified axis. */\n  lockAxis;\n  /**\n   * Selector that will be used to determine the root draggable element, starting from\n   * the `cdkDrag` element and going up the DOM. Passing an alternate root element is useful\n   * when trying to enable dragging on an element that you might not have access to.\n   */\n  rootElementSelector;\n  /**\n   * Node or selector that will be used to determine the element to which the draggable's\n   * position will be constrained. If a string is passed in, it'll be used as a selector that\n   * will be matched starting from the element's parent and going up the DOM until a match\n   * has been found.\n   */\n  boundaryElement;\n  /**\n   * Amount of milliseconds to wait after the user has put their\n   * pointer down before starting to drag the element.\n   */\n  dragStartDelay;\n  /**\n   * Sets the position of a `CdkDrag` that is outside of a drop container.\n   * Can be used to restore the element's position for a returning user.\n   */\n  freeDragPosition;\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || !!(this.dropContainer && this.dropContainer.disabled);\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._dragRef.disabled = this._disabled;\n  }\n  _disabled;\n  /**\n   * Function that can be used to customize the logic of how the position of the drag item\n   * is limited while it's being dragged. Gets called with a point containing the current position\n   * of the user's pointer on the page, a reference to the item being dragged and its dimensions.\n   * Should return a point describing where the item should be rendered.\n   */\n  constrainPosition;\n  /** Class to be added to the preview element. */\n  previewClass;\n  /**\n   * Configures the place into which the preview of the item will be inserted. Can be configured\n   * globally through `CDK_DROP_LIST`. Possible values:\n   * - `global` - Preview will be inserted at the bottom of the `<body>`. The advantage is that\n   * you don't have to worry about `overflow: hidden` or `z-index`, but the item won't retain\n   * its inherited styles.\n   * - `parent` - Preview will be inserted into the parent of the drag item. The advantage is that\n   * inherited styles will be preserved, but it may be clipped by `overflow: hidden` or not be\n   * visible due to `z-index`. Furthermore, the preview is going to have an effect over selectors\n   * like `:nth-child` and some flexbox configurations.\n   * - `ElementRef<HTMLElement> | HTMLElement` - Preview will be inserted into a specific element.\n   * Same advantages and disadvantages as `parent`.\n   */\n  previewContainer;\n  /**\n   * If the parent of the dragged element has a `scale` transform, it can throw off the\n   * positioning when the user starts dragging. Use this input to notify the CDK of the scale.\n   */\n  scale = 1;\n  /** Emits when the user starts dragging the item. */\n  started = new EventEmitter();\n  /** Emits when the user has released a drag item, before any animations have started. */\n  released = new EventEmitter();\n  /** Emits when the user stops dragging an item in the container. */\n  ended = new EventEmitter();\n  /** Emits when the user has moved the item into a new container. */\n  entered = new EventEmitter();\n  /** Emits when the user removes the item its container by dragging it into another container. */\n  exited = new EventEmitter();\n  /** Emits when the user drops the item inside a container. */\n  dropped = new EventEmitter();\n  /**\n   * Emits as the user is dragging the item. Use with caution,\n   * because this event will fire for every pixel that the user has dragged.\n   */\n  moved = new Observable(observer => {\n    const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n      source: this,\n      pointerPosition: movedEvent.pointerPosition,\n      event: movedEvent.event,\n      delta: movedEvent.delta,\n      distance: movedEvent.distance\n    }))).subscribe(observer);\n    return () => {\n      subscription.unsubscribe();\n    };\n  });\n  _injector = inject(Injector);\n  constructor() {\n    const dropContainer = this.dropContainer;\n    const config = inject(CDK_DRAG_CONFIG, {\n      optional: true\n    });\n    const dragDrop = inject(DragDrop);\n    this._dragRef = dragDrop.createDrag(this.element, {\n      dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n      zIndex: config?.zIndex\n    });\n    this._dragRef.data = this;\n    this._dragDropRegistry.registerDirectiveNode(this.element.nativeElement, this);\n    if (config) {\n      this._assignDefaults(config);\n    }\n    // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n    if (dropContainer) {\n      this._dragRef._withDropContainer(dropContainer._dropListRef);\n      dropContainer.addItem(this);\n      // The drop container reads this so we need to sync it here.\n      dropContainer._dropListRef.beforeStarted.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._dragRef.scale = this.scale;\n      });\n    }\n    this._syncInputs(this._dragRef);\n    this._handleEvents(this._dragRef);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._dragRef.getPlaceholderElement();\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._dragRef.getRootElement();\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._dragRef.reset();\n  }\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    return this._dragRef.getFreeDragPosition();\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._dragRef.setFreeDragPosition(value);\n  }\n  ngAfterViewInit() {\n    // We need to wait until after render, in order for the reference\n    // element to be in the proper place in the DOM. This is mostly relevant\n    // for draggable elements inside portals since they get stamped out in\n    // their original DOM position, and then they get transferred to the portal.\n    afterNextRender(() => {\n      this._updateRootElement();\n      this._setupHandlesListener();\n      this._dragRef.scale = this.scale;\n      if (this.freeDragPosition) {\n        this._dragRef.setFreeDragPosition(this.freeDragPosition);\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  ngOnChanges(changes) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition'];\n    // We don't have to react to the first change since it's being\n    // handled in the `afterNextRender` queued up in the constructor.\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    }\n    // Scale affects the free drag position so we need to sync it up here.\n    this._dragRef.scale = this.scale;\n    // Skip the first change since it's being handled in the `afterNextRender` queued up in the\n    // constructor.\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n    this._dragDropRegistry.removeDirectiveNode(this.element.nativeElement);\n    // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n    this._ngZone.runOutsideAngular(() => {\n      this._handles.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._dragRef.dispose();\n    });\n  }\n  _addHandle(handle) {\n    const handles = this._handles.getValue();\n    handles.push(handle);\n    this._handles.next(handles);\n  }\n  _removeHandle(handle) {\n    const handles = this._handles.getValue();\n    const index = handles.indexOf(handle);\n    if (index > -1) {\n      handles.splice(index, 1);\n      this._handles.next(handles);\n    }\n  }\n  _setPreviewTemplate(preview) {\n    this._previewTemplate = preview;\n  }\n  _resetPreviewTemplate(preview) {\n    if (preview === this._previewTemplate) {\n      this._previewTemplate = null;\n    }\n  }\n  _setPlaceholderTemplate(placeholder) {\n    this._placeholderTemplate = placeholder;\n  }\n  _resetPlaceholderTemplate(placeholder) {\n    if (placeholder === this._placeholderTemplate) {\n      this._placeholderTemplate = null;\n    }\n  }\n  /** Syncs the root element with the `DragRef`. */\n  _updateRootElement() {\n    const element = this.element.nativeElement;\n    let rootElement = element;\n    if (this.rootElementSelector) {\n      rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) :\n      // Comment tag doesn't have closest method, so use parent's one.\n      element.parentElement?.closest(this.rootElementSelector);\n    }\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n    this._dragRef.withRootElement(rootElement || element);\n  }\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n  _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n    if (!boundary) {\n      return null;\n    }\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest(boundary);\n    }\n    return coerceElement(boundary);\n  }\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n  _syncInputs(ref) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate ? {\n          template: this._placeholderTemplate.templateRef,\n          context: this._placeholderTemplate.data,\n          viewContainer: this._viewContainerRef\n        } : null;\n        const preview = this._previewTemplate ? {\n          template: this._previewTemplate.templateRef,\n          context: this._previewTemplate.data,\n          matchSize: this._previewTemplate.matchSize,\n          viewContainer: this._viewContainerRef\n        } : null;\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.scale = this.scale;\n        ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    });\n    // This only needs to be resolved once.\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      // If we managed to resolve a parent through DI, use it.\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      }\n      // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        const parentDrag = this._dragDropRegistry.getDragDirectiveForNode(parent);\n        if (parentDrag) {\n          ref.withParent(parentDrag._dragRef);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    });\n  }\n  /** Handles the events from the underlying `DragRef`. */\n  _handleEvents(ref) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({\n        source: this,\n        event: startEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({\n        source: this,\n        event: releaseEvent.event\n      });\n    });\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex\n      });\n    });\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n    });\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n  /** Sets up the listener that syncs the handles with the drag ref. */\n  _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles.pipe(\n    // Sync the new handles with the DragRef.\n    tap(handles => {\n      const handleElements = handles.map(handle => handle.element);\n      // Usually handles are only allowed to be a descendant of the drag element, but if\n      // the consumer defined a different drag root, we should allow the drag element\n      // itself to be a handle too.\n      if (this._selfHandle && this.rootElementSelector) {\n        handleElements.push(this.element);\n      }\n      this._dragRef.withHandles(handleElements);\n    }),\n    // Listen if the state of any of the handles changes.\n    switchMap(handles => {\n      return merge(...handles.map(item => item._stateChanges.pipe(startWith(item))));\n    }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n      // Enabled/disable the handle that changed in the DragRef.\n      const dragRef = this._dragRef;\n      const handle = handleInstance.element.nativeElement;\n      handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n    });\n  }\n  static ɵfac = function CdkDrag_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDrag)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDrag,\n    selectors: [[\"\", \"cdkDrag\", \"\"]],\n    hostAttrs: [1, \"cdk-drag\"],\n    hostVars: 4,\n    hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n      }\n    },\n    inputs: {\n      data: [0, \"cdkDragData\", \"data\"],\n      lockAxis: [0, \"cdkDragLockAxis\", \"lockAxis\"],\n      rootElementSelector: [0, \"cdkDragRootElement\", \"rootElementSelector\"],\n      boundaryElement: [0, \"cdkDragBoundary\", \"boundaryElement\"],\n      dragStartDelay: [0, \"cdkDragStartDelay\", \"dragStartDelay\"],\n      freeDragPosition: [0, \"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n      disabled: [2, \"cdkDragDisabled\", \"disabled\", booleanAttribute],\n      constrainPosition: [0, \"cdkDragConstrainPosition\", \"constrainPosition\"],\n      previewClass: [0, \"cdkDragPreviewClass\", \"previewClass\"],\n      previewContainer: [0, \"cdkDragPreviewContainer\", \"previewContainer\"],\n      scale: [2, \"cdkDragScale\", \"scale\", numberAttribute]\n    },\n    outputs: {\n      started: \"cdkDragStarted\",\n      released: \"cdkDragReleased\",\n      ended: \"cdkDragEnded\",\n      entered: \"cdkDragEntered\",\n      exited: \"cdkDragExited\",\n      dropped: \"cdkDragDropped\",\n      moved: \"cdkDragMoved\"\n    },\n    exportAs: [\"cdkDrag\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_PARENT,\n      useExisting: CdkDrag\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDrag, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDrag]',\n      exportAs: 'cdkDrag',\n      host: {\n        'class': 'cdk-drag',\n        '[class.cdk-drag-disabled]': 'disabled',\n        '[class.cdk-drag-dragging]': '_dragRef.isDragging()'\n      },\n      providers: [{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]\n    }]\n  }], () => [], {\n    data: [{\n      type: Input,\n      args: ['cdkDragData']\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDragLockAxis']\n    }],\n    rootElementSelector: [{\n      type: Input,\n      args: ['cdkDragRootElement']\n    }],\n    boundaryElement: [{\n      type: Input,\n      args: ['cdkDragBoundary']\n    }],\n    dragStartDelay: [{\n      type: Input,\n      args: ['cdkDragStartDelay']\n    }],\n    freeDragPosition: [{\n      type: Input,\n      args: ['cdkDragFreeDragPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    constrainPosition: [{\n      type: Input,\n      args: ['cdkDragConstrainPosition']\n    }],\n    previewClass: [{\n      type: Input,\n      args: ['cdkDragPreviewClass']\n    }],\n    previewContainer: [{\n      type: Input,\n      args: ['cdkDragPreviewContainer']\n    }],\n    scale: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragScale',\n        transform: numberAttribute\n      }]\n    }],\n    started: [{\n      type: Output,\n      args: ['cdkDragStarted']\n    }],\n    released: [{\n      type: Output,\n      args: ['cdkDragReleased']\n    }],\n    ended: [{\n      type: Output,\n      args: ['cdkDragEnded']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDragEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDragExited']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDragDropped']\n    }],\n    moved: [{\n      type: Output,\n      args: ['cdkDragMoved']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n  /** Drop lists registered inside the group. */\n  _items = new Set();\n  /** Whether starting a dragging sequence from inside this group is disabled. */\n  disabled = false;\n  ngOnDestroy() {\n    this._items.clear();\n  }\n  static ɵfac = function CdkDropListGroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDropListGroup)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDropListGroup,\n    selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n    inputs: {\n      disabled: [2, \"cdkDropListGroupDisabled\", \"disabled\", booleanAttribute]\n    },\n    exportAs: [\"cdkDropListGroup\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DROP_LIST_GROUP,\n      useExisting: CdkDropListGroup\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropListGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropListGroup]',\n      exportAs: 'cdkDropListGroup',\n      providers: [{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListGroupDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n  element = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _scrollDispatcher = inject(ScrollDispatcher);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _group = inject(CDK_DROP_LIST_GROUP, {\n    optional: true,\n    skipSelf: true\n  });\n  /** Refs that have been synced with the drop ref most recently. */\n  _latestSortedRefs;\n  /** Emits when the list has been destroyed. */\n  _destroyed = new Subject();\n  /** Whether the element's scrollable parents have been resolved. */\n  _scrollableParentsResolved;\n  /** Keeps track of the drop lists that are currently on the page. */\n  static _dropLists = [];\n  /** Reference to the underlying drop list instance. */\n  _dropListRef;\n  /**\n   * Other draggable containers that this container is connected to and into which the\n   * container's items can be transferred. Can either be references to other drop containers,\n   * or their unique IDs.\n   */\n  connectedTo = [];\n  /** Arbitrary data to attach to this container. */\n  data;\n  /** Direction in which the list is oriented. */\n  orientation;\n  /**\n   * Unique ID for the drop zone. Can be used as a reference\n   * in the `connectedTo` of another `CdkDropList`.\n   */\n  id = inject(_IdGenerator).getId('cdk-drop-list-');\n  /** Locks the position of the draggable elements inside the container along the specified axis. */\n  lockAxis;\n  /** Whether starting a dragging sequence from this container is disabled. */\n  get disabled() {\n    return this._disabled || !!this._group && this._group.disabled;\n  }\n  set disabled(value) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = value;\n  }\n  _disabled;\n  /** Whether sorting within this drop list is disabled. */\n  sortingDisabled;\n  /**\n   * Function that is used to determine whether an item\n   * is allowed to be moved into a drop container.\n   */\n  enterPredicate = () => true;\n  /** Functions that is used to determine whether an item can be sorted into a particular index. */\n  sortPredicate = () => true;\n  /** Whether to auto-scroll the view when the user moves their pointer close to the edges. */\n  autoScrollDisabled;\n  /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n  autoScrollStep;\n  /**\n   * Selector that will be used to resolve an alternate element container for the drop list.\n   * Passing an alternate container is useful for the cases where one might not have control\n   * over the parent node of the draggable items within the list (e.g. due to content projection).\n   * This allows for usages like:\n   *\n   * ```\n   * <div cdkDropList cdkDropListElementContainer=\".inner\">\n   *   <div class=\"inner\">\n   *     <div cdkDrag></div>\n   *   </div>\n   * </div>\n   * ```\n   */\n  elementContainerSelector;\n  /** Emits when the user drops an item inside the container. */\n  dropped = new EventEmitter();\n  /**\n   * Emits when the user has moved a new drag item into this container.\n   */\n  entered = new EventEmitter();\n  /**\n   * Emits when the user removes an item from the container\n   * by dragging it into another container.\n   */\n  exited = new EventEmitter();\n  /** Emits as the user is swapping items while actively dragging. */\n  sorted = new EventEmitter();\n  /**\n   * Keeps track of the items that are registered with this container. Historically we used to\n   * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n   * well which means that we can't handle cases like dragging the headers of a `mat-table`\n   * correctly. What we do instead is to have the items register themselves with the container\n   * and then we sort them based on their position in the DOM.\n   */\n  _unsortedItems = new Set();\n  constructor() {\n    const dragDrop = inject(DragDrop);\n    const config = inject(CDK_DRAG_CONFIG, {\n      optional: true\n    });\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(this.element.nativeElement, 'cdkDropList');\n    }\n    this._dropListRef = dragDrop.createDropList(this.element);\n    this._dropListRef.data = this;\n    if (config) {\n      this._assignDefaults(config);\n    }\n    this._dropListRef.enterPredicate = (drag, drop) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n    this._dropListRef.sortPredicate = (index, drag, drop) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n    this._setupInputSyncSubscription(this._dropListRef);\n    this._handleEvents(this._dropListRef);\n    CdkDropList._dropLists.push(this);\n    if (this._group) {\n      this._group._items.add(this);\n    }\n  }\n  /** Registers an items with the drop list. */\n  addItem(item) {\n    this._unsortedItems.add(item);\n    // Only sync the items while dragging since this method is\n    // called when items are being initialized one-by-one.\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef(this.getSortedItems().map(item => item._dragRef));\n    }\n  }\n  /** Removes an item from the drop list. */\n  removeItem(item) {\n    this._unsortedItems.delete(item);\n    // This method might be called on destroy so we always want to sync with the ref.\n    // Note that we reuse the last set of synced items, rather than re-sorting the whole\n    // list, because it can slow down re-renders of large lists (see #30737).\n    if (this._latestSortedRefs) {\n      const index = this._latestSortedRefs.indexOf(item._dragRef);\n      if (index > -1) {\n        this._latestSortedRefs.splice(index, 1);\n        this._syncItemsWithRef(this._latestSortedRefs);\n      }\n    }\n  }\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n  getSortedItems() {\n    return Array.from(this._unsortedItems).sort((a, b) => {\n      const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement());\n      // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n    this._latestSortedRefs = undefined;\n    this._unsortedItems.clear();\n    this._dropListRef.dispose();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n  _setupInputSyncSubscription(ref) {\n    if (this._dir) {\n      this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n    }\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n          return correspondingDropList;\n        }\n        return drop;\n      });\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      }\n      // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n        this._dropListRef.withScrollableParents(scrollableParents);\n        // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n        this._scrollableParentsResolved = true;\n      }\n      if (this.elementContainerSelector) {\n        const container = this.element.nativeElement.querySelector(this.elementContainerSelector);\n        if (!container && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw new Error(`CdkDropList could not find an element container matching the selector \"${this.elementContainerSelector}\"`);\n        }\n        ref.withElementContainer(container);\n      }\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = this.sortingDisabled;\n      ref.autoScrollDisabled = this.autoScrollDisabled;\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n    });\n  }\n  /** Handles events from the underlying DropListRef. */\n  _handleEvents(ref) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef(this.getSortedItems().map(item => item._dragRef));\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex\n      });\n    });\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data\n      });\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n      // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n      this._changeDetectorRef.markForCheck();\n    });\n    merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      draggingDisabled,\n      sortingDisabled,\n      listAutoScrollDisabled,\n      listOrientation\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n  /** Syncs up the registered drag items with underlying drop list ref. */\n  _syncItemsWithRef(items) {\n    this._latestSortedRefs = items;\n    this._dropListRef.withItems(items);\n  }\n  static ɵfac = function CdkDropList_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDropList)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDropList,\n    selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n    hostAttrs: [1, \"cdk-drop-list\"],\n    hostVars: 7,\n    hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.id);\n        i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n      }\n    },\n    inputs: {\n      connectedTo: [0, \"cdkDropListConnectedTo\", \"connectedTo\"],\n      data: [0, \"cdkDropListData\", \"data\"],\n      orientation: [0, \"cdkDropListOrientation\", \"orientation\"],\n      id: \"id\",\n      lockAxis: [0, \"cdkDropListLockAxis\", \"lockAxis\"],\n      disabled: [2, \"cdkDropListDisabled\", \"disabled\", booleanAttribute],\n      sortingDisabled: [2, \"cdkDropListSortingDisabled\", \"sortingDisabled\", booleanAttribute],\n      enterPredicate: [0, \"cdkDropListEnterPredicate\", \"enterPredicate\"],\n      sortPredicate: [0, \"cdkDropListSortPredicate\", \"sortPredicate\"],\n      autoScrollDisabled: [2, \"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\", booleanAttribute],\n      autoScrollStep: [0, \"cdkDropListAutoScrollStep\", \"autoScrollStep\"],\n      elementContainerSelector: [0, \"cdkDropListElementContainer\", \"elementContainerSelector\"]\n    },\n    outputs: {\n      dropped: \"cdkDropListDropped\",\n      entered: \"cdkDropListEntered\",\n      exited: \"cdkDropListExited\",\n      sorted: \"cdkDropListSorted\"\n    },\n    exportAs: [\"cdkDropList\"],\n    features: [i0.ɵɵProvidersFeature([\n    // Prevent child drop lists from picking up the same group as their parent.\n    {\n      provide: CDK_DROP_LIST_GROUP,\n      useValue: undefined\n    }, {\n      provide: CDK_DROP_LIST,\n      useExisting: CdkDropList\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropList, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropList], cdk-drop-list',\n      exportAs: 'cdkDropList',\n      providers: [\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }],\n      host: {\n        'class': 'cdk-drop-list',\n        '[attr.id]': 'id',\n        '[class.cdk-drop-list-disabled]': 'disabled',\n        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()'\n      }\n    }]\n  }], () => [], {\n    connectedTo: [{\n      type: Input,\n      args: ['cdkDropListConnectedTo']\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDropListData']\n    }],\n    orientation: [{\n      type: Input,\n      args: ['cdkDropListOrientation']\n    }],\n    id: [{\n      type: Input\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDropListLockAxis']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    sortingDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListSortingDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    enterPredicate: [{\n      type: Input,\n      args: ['cdkDropListEnterPredicate']\n    }],\n    sortPredicate: [{\n      type: Input,\n      args: ['cdkDropListSortPredicate']\n    }],\n    autoScrollDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListAutoScrollDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    autoScrollStep: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollStep']\n    }],\n    elementContainerSelector: [{\n      type: Input,\n      args: ['cdkDropListElementContainer']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDropListDropped']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDropListEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDropListExited']\n    }],\n    sorted: [{\n      type: Output,\n      args: ['cdkDropListSorted']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n  templateRef = inject(TemplateRef);\n  _drag = inject(CDK_DRAG_PARENT, {\n    optional: true\n  });\n  /** Context data to be added to the preview template instance. */\n  data;\n  /** Whether the preview should preserve the same size as the item that is being dragged. */\n  matchSize = false;\n  constructor() {\n    this._drag?._setPreviewTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPreviewTemplate(this);\n  }\n  static ɵfac = function CdkDragPreview_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDragPreview)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDragPreview,\n    selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n    inputs: {\n      data: \"data\",\n      matchSize: [2, \"matchSize\", \"matchSize\", booleanAttribute]\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_PREVIEW,\n      useExisting: CdkDragPreview\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPreview, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPreview]',\n      providers: [{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]\n    }]\n  }], () => [], {\n    data: [{\n      type: Input\n    }],\n    matchSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n  templateRef = inject(TemplateRef);\n  _drag = inject(CDK_DRAG_PARENT, {\n    optional: true\n  });\n  /** Context data to be added to the placeholder template instance. */\n  data;\n  constructor() {\n    this._drag?._setPlaceholderTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPlaceholderTemplate(this);\n  }\n  static ɵfac = function CdkDragPlaceholder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDragPlaceholder)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkDragPlaceholder,\n    selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n    inputs: {\n      data: \"data\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: CDK_DRAG_PLACEHOLDER,\n      useExisting: CdkDragPlaceholder\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPlaceholder, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPlaceholder]',\n      providers: [{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }]\n    }]\n  }], () => [], {\n    data: [{\n      type: Input\n    }]\n  });\n})();\nconst DRAG_DROP_DIRECTIVES = [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder];\nclass DragDropModule {\n  static ɵfac = function DragDropModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DragDropModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DragDropModule,\n    imports: [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder],\n    exports: [CdkScrollableModule, CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DragDrop],\n    imports: [CdkScrollableModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropModule, [{\n    type: NgModule,\n    args: [{\n      imports: DRAG_DROP_DIRECTIVES,\n      exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n      providers: [DragDrop]\n    }]\n  }], null, null);\n})();\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,SAAS,cAAc,MAAM;AAC3B,QAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAM,oBAAoB,MAAM,iBAAiB,MAAM;AACvD,QAAM,WAAW,KAAK,SAAS,YAAY;AAE3C,QAAM,gBAAgB,IAAI;AAC1B,WAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,sBAAkB,CAAC,EAAE,gBAAgB,IAAI;AAAA,EAC3C;AACA,MAAI,aAAa,UAAU;AACzB,uBAAmB,MAAM,KAAK;AAAA,EAChC,WAAW,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY;AACnF,sBAAkB,MAAM,KAAK;AAAA,EAC/B;AACA,eAAa,UAAU,MAAM,OAAO,kBAAkB;AACtD,eAAa,2BAA2B,MAAM,OAAO,iBAAiB;AACtE,SAAO;AACT;AAEA,SAAS,aAAa,UAAU,MAAM,OAAO,UAAU;AACrD,QAAM,qBAAqB,KAAK,iBAAiB,QAAQ;AACzD,MAAI,mBAAmB,QAAQ;AAC7B,UAAM,gBAAgB,MAAM,iBAAiB,QAAQ;AACrD,aAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,eAAS,mBAAmB,CAAC,GAAG,cAAc,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACF;AAEA,IAAI,gBAAgB;AAEpB,SAAS,kBAAkB,QAAQ,OAAO;AAExC,MAAI,MAAM,SAAS,QAAQ;AACzB,UAAM,QAAQ,OAAO;AAAA,EACvB;AAIA,MAAI,MAAM,SAAS,WAAW,MAAM,MAAM;AACxC,UAAM,OAAO,aAAa,MAAM,IAAI,IAAI,eAAe;AAAA,EACzD;AACF;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,QAAM,UAAU,MAAM,WAAW,IAAI;AACrC,MAAI,SAAS;AAGX,QAAI;AACF,cAAQ,UAAU,QAAQ,GAAG,CAAC;AAAA,IAChC,QAAQ;AAAA,IAAC;AAAA,EACX;AACF;AAGA,SAAS,qBAAqB,SAAS;AACrC,QAAM,OAAO,QAAQ,sBAAsB;AAK3C,SAAO;AAAA,IACL,KAAK,KAAK;AAAA,IACV,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,MAAM,KAAK;AAAA,IACX,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACV;AACF;AAOA,SAAS,mBAAmB,YAAY,GAAG,GAAG;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,KAAK,OAAO,KAAK,UAAU,KAAK,QAAQ,KAAK;AACtD;AAOA,SAAS,cAAc,SAAS,KAAK,MAAM;AACzC,UAAQ,OAAO;AACf,UAAQ,SAAS,QAAQ,MAAM,QAAQ;AACvC,UAAQ,QAAQ;AAChB,UAAQ,QAAQ,QAAQ,OAAO,QAAQ;AACzC;AAQA,SAAS,qBAAqB,MAAM,WAAW,UAAU,UAAU;AACjE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,QAAQ;AAC3B,QAAM,aAAa,SAAS;AAC5B,SAAO,WAAW,MAAM,cAAc,WAAW,SAAS,cAAc,WAAW,OAAO,cAAc,WAAW,QAAQ;AAC7H;AAGA,IAAM,wBAAN,MAA4B;AAAA,EAC1B;AAAA;AAAA,EAEA,YAAY,oBAAI,IAAI;AAAA,EACpB,YAAY,WAAW;AACrB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA,EAEA,MAAM,UAAU;AACd,SAAK,MAAM;AACX,SAAK,UAAU,IAAI,KAAK,WAAW;AAAA,MACjC,gBAAgB,KAAK,0BAA0B;AAAA,IACjD,CAAC;AACD,aAAS,QAAQ,aAAW;AAC1B,WAAK,UAAU,IAAI,SAAS;AAAA,QAC1B,gBAAgB;AAAA,UACd,KAAK,QAAQ;AAAA,UACb,MAAM,QAAQ;AAAA,QAChB;AAAA,QACA,YAAY,qBAAqB,OAAO;AAAA,MAC1C,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,UAAM,SAAS,gBAAgB,KAAK;AACpC,UAAM,iBAAiB,KAAK,UAAU,IAAI,MAAM;AAChD,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,eAAe;AACtC,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,KAAK,WAAW;AAC7B,YAAM,yBAAyB,KAAK,0BAA0B;AAC9D,eAAS,uBAAuB;AAChC,gBAAU,uBAAuB;AAAA,IACnC,OAAO;AACL,eAAS,OAAO;AAChB,gBAAU,OAAO;AAAA,IACnB;AACA,UAAM,gBAAgB,eAAe,MAAM;AAC3C,UAAM,iBAAiB,eAAe,OAAO;AAG7C,SAAK,UAAU,QAAQ,CAAC,UAAU,SAAS;AACzC,UAAI,SAAS,cAAc,WAAW,QAAQ,OAAO,SAAS,IAAI,GAAG;AACnE,sBAAc,SAAS,YAAY,eAAe,cAAc;AAAA,MAClE;AAAA,IACF,CAAC;AACD,mBAAe,MAAM;AACrB,mBAAe,OAAO;AACtB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,4BAA4B;AAC1B,WAAO;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,MAAM,OAAO;AAAA,IACf;AAAA,EACF;AACF;AAMA,SAAS,YAAY,SAAS,WAAW;AACvC,QAAM,YAAY,QAAQ;AAC1B,MAAI,UAAU,WAAW,KAAK,UAAU,CAAC,EAAE,aAAa,UAAU,cAAc;AAC9E,WAAO,UAAU,CAAC;AAAA,EACpB;AACA,QAAM,UAAU,UAAU,cAAc,KAAK;AAC7C,YAAU,QAAQ,UAAQ,QAAQ,YAAY,IAAI,CAAC;AACnD,SAAO;AACT;AAOA,SAAS,aAAa,MAAM,QAAQA,sBAAqB;AACvD,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,YAAM,QAAQ,OAAO,GAAG;AACxB,UAAI,OAAO;AACT,aAAK,YAAY,KAAK,OAAOA,sBAAqB,IAAI,GAAG,IAAI,cAAc,EAAE;AAAA,MAC/E,OAAO;AACL,aAAK,eAAe,GAAG;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,6BAA6B,SAAS,QAAQ;AACrD,QAAM,aAAa,SAAS,KAAK;AACjC,eAAa,QAAQ,OAAO;AAAA,IAC1B,gBAAgB,SAAS,KAAK;AAAA,IAC9B,qBAAqB,SAAS,KAAK;AAAA,IACnC,+BAA+B,SAAS,KAAK;AAAA,IAC7C,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,EACtB,CAAC;AACH;AAQA,SAAS,iBAAiB,SAAS,QAAQA,sBAAqB;AAC9D,eAAa,QAAQ,OAAO;AAAA,IAC1B,UAAU,SAAS,KAAK;AAAA,IACxB,KAAK,SAAS,KAAK;AAAA,IACnB,SAAS,SAAS,KAAK;AAAA,IACvB,MAAM,SAAS,KAAK;AAAA,EACtB,GAAGA,oBAAmB;AACxB;AAKA,SAAS,kBAAkB,WAAW,kBAAkB;AACtD,SAAO,oBAAoB,oBAAoB,SAAS,YAAY,MAAM,mBAAmB;AAC/F;AAMA,SAAS,iBAAiB,QAAQ,YAAY;AAC5C,SAAO,MAAM,QAAQ,GAAG,WAAW,KAAK;AACxC,SAAO,MAAM,SAAS,GAAG,WAAW,MAAM;AAC1C,SAAO,MAAM,YAAY,aAAa,WAAW,MAAM,WAAW,GAAG;AACvE;AAMA,SAAS,aAAa,GAAG,GAAG;AAG1B,SAAO,eAAe,KAAK,MAAM,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC;AACzD;AAGA,SAAS,sBAAsB,OAAO;AAEpC,QAAM,aAAa,MAAM,YAAY,EAAE,QAAQ,IAAI,IAAI,KAAK,IAAI;AAChE,SAAO,WAAW,KAAK,IAAI;AAC7B;AAEA,SAAS,mCAAmC,SAAS;AACnD,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,yBAAyB,sBAAsB,eAAe,qBAAqB;AACzF,QAAM,WAAW,uBAAuB,KAAK,UAAQ,SAAS,eAAe,SAAS,KAAK;AAE3F,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAGA,QAAM,gBAAgB,uBAAuB,QAAQ,QAAQ;AAC7D,QAAM,eAAe,sBAAsB,eAAe,qBAAqB;AAC/E,QAAM,YAAY,sBAAsB,eAAe,kBAAkB;AACzE,SAAO,sBAAsB,aAAa,aAAa,CAAC,IAAI,sBAAsB,UAAU,aAAa,CAAC;AAC5G;AAEA,SAAS,sBAAsB,eAAe,MAAM;AAClD,QAAM,QAAQ,cAAc,iBAAiB,IAAI;AACjD,SAAO,MAAM,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AACjD;AAGA,IAAM,sBAAsB,oBAAI,IAAI;AAAA;AAAA,EAEpC;AAAU,CAAC;AACX,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,WAAW,cAAc,YAAY,iBAAiB,kBAAkB,eAAe,uBAAuB,mBAAmB,SAAS,WAAW;AAC/J,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,wBAAwB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,OAAO,QAAQ;AACb,SAAK,WAAW,KAAK,eAAe;AACpC,WAAO,YAAY,KAAK,QAAQ;AAGhC,QAAI,gBAAgB,KAAK,QAAQ,GAAG;AAClC,WAAK,SAAS,aAAa,EAAE;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,SAAS,OAAO;AACrB,SAAK,sBAAsB,QAAQ;AACnC,SAAK,WAAW,KAAK,uBAAuB;AAAA,EAC9C;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,SAAS,MAAM,YAAY;AAAA,EAClC;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK,SAAS,sBAAsB;AAAA,EAC7C;AAAA,EACA,SAAS,WAAW;AAClB,SAAK,SAAS,UAAU,IAAI,SAAS;AAAA,EACvC;AAAA,EACA,wBAAwB;AACtB,WAAO,mCAAmC,KAAK,QAAQ;AAAA,EACzD;AAAA,EACA,iBAAiB,MAAM,SAAS;AAC9B,WAAO,KAAK,UAAU,OAAO,KAAK,UAAU,MAAM,OAAO;AAAA,EAC3D;AAAA,EACA,iBAAiB;AACf,UAAM,gBAAgB,KAAK;AAC3B,UAAM,eAAe,KAAK;AAC1B,UAAM,kBAAkB,gBAAgB,cAAc,WAAW;AACjE,QAAI;AACJ,QAAI,mBAAmB,eAAe;AAGpC,YAAM,WAAW,cAAc,YAAY,KAAK,kBAAkB;AAClE,YAAM,UAAU,cAAc,cAAc,mBAAmB,iBAAiB,cAAc,OAAO;AACrG,cAAQ,cAAc;AACtB,gBAAU,YAAY,SAAS,KAAK,SAAS;AAC7C,WAAK,uBAAuB;AAC5B,UAAI,cAAc,WAAW;AAC3B,yBAAiB,SAAS,QAAQ;AAAA,MACpC,OAAO;AACL,gBAAQ,MAAM,YAAY,aAAa,KAAK,sBAAsB,GAAG,KAAK,sBAAsB,CAAC;AAAA,MACnG;AAAA,IACF,OAAO;AACL,gBAAU,cAAc,KAAK,YAAY;AACzC,uBAAiB,SAAS,KAAK,eAAe;AAC9C,UAAI,KAAK,mBAAmB;AAC1B,gBAAQ,MAAM,YAAY,KAAK;AAAA,MACjC;AAAA,IACF;AACA,iBAAa,QAAQ,OAAO;AAAA;AAAA;AAAA,MAG1B,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlB,UAAU,gBAAgB,OAAO,IAAI,eAAe;AAAA,MACpD,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW,KAAK,UAAU;AAAA,IAC5B,GAAG,mBAAmB;AACtB,iCAA6B,SAAS,KAAK;AAC3C,YAAQ,UAAU,IAAI,kBAAkB;AACxC,YAAQ,aAAa,WAAW,QAAQ;AACxC,YAAQ,aAAa,OAAO,KAAK,UAAU;AAC3C,QAAI,cAAc;AAChB,UAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,qBAAa,QAAQ,eAAa,QAAQ,UAAU,IAAI,SAAS,CAAC;AAAA,MACpE,OAAO;AACL,gBAAQ,UAAU,IAAI,YAAY;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,gBAAgB,SAAS;AAChC,SAAO,iBAAiB;AAC1B;AAGA,IAAM,8BAA8B;AAAA,EAClC,SAAS;AACX;AAEA,IAAM,6BAA6B;AAAA,EACjC,SAAS;AACX;AAEA,IAAM,gCAAgC;AAAA,EACpC,SAAS;AAAA,EACT,SAAS;AACX;AAOA,IAAM,0BAA0B;AAEhC,IAAM,0BAA0B,oBAAI,IAAI;AAAA;AAAA,EAExC;AAAU,CAAC;AAIX,IAAM,UAAN,MAAc;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB;AAAA,IAClB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAAA;AAAA,EAEA,mBAAmB;AAAA,IACjB,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,OAAO,KAAK;AAAA;AAAA,EAElC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,cAAc,IAAI,QAAQ;AAAA;AAAA,EAE1B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,2BAA2B,aAAa;AAAA;AAAA,EAExC,yBAAyB,aAAa;AAAA;AAAA,EAEtC,sBAAsB,aAAa;AAAA;AAAA,EAEnC,sBAAsB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,mBAAmB;AAAA;AAAA,EAEnB,6BAA6B;AAAA;AAAA,EAE7B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,WAAW,CAAC;AAAA;AAAA,EAEZ,mBAAmB,oBAAI,IAAI;AAAA;AAAA,EAE3B;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAAA;AAAA,EAER,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,CAAC,EAAE,KAAK,kBAAkB,KAAK,eAAe;AAAA,EACzE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,WAAW;AAC5B,WAAK,YAAY;AACjB,WAAK,8BAA8B;AACnC,WAAK,SAAS,QAAQ,YAAU,6BAA6B,QAAQ,KAAK,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,gBAAgB,IAAI,QAAQ;AAAA;AAAA,EAE5B,UAAU,IAAI,QAAQ;AAAA;AAAA,EAEtB,WAAW,IAAI,QAAQ;AAAA;AAAA,EAEvB,QAAQ,IAAI,QAAQ;AAAA;AAAA,EAEpB,UAAU,IAAI,QAAQ;AAAA;AAAA,EAEtB,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,UAAU,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,QAAQ,KAAK;AAAA;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA,EACA,YAAY,SAAS,SAAS,WAAW,SAAS,gBAAgB,mBAAmB,WAAW;AAC9F,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,oBAAoB;AACzB,SAAK,YAAY;AACjB,SAAK,gBAAgB,OAAO,EAAE,WAAW,QAAQ,iBAAiB,IAAI;AACtE,SAAK,mBAAmB,IAAI,sBAAsB,SAAS;AAC3D,sBAAkB,iBAAiB,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,WAAO,KAAK,WAAW,IAAI,KAAK,sBAAsB,IAAI,KAAK,eAAe;AAAA,EAChF;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,SAAK,WAAW,QAAQ,IAAI,YAAU,cAAc,MAAM,CAAC;AAC3D,SAAK,SAAS,QAAQ,YAAU,6BAA6B,QAAQ,KAAK,QAAQ,CAAC;AACnF,SAAK,8BAA8B;AAKnC,UAAM,kBAAkB,oBAAI,IAAI;AAChC,SAAK,iBAAiB,QAAQ,YAAU;AACtC,UAAI,KAAK,SAAS,QAAQ,MAAM,IAAI,IAAI;AACtC,wBAAgB,IAAI,MAAM;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,UAAU;AAC5B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,UAAU;AAChC,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,aAAa;AAC3B,UAAM,UAAU,cAAc,WAAW;AACzC,QAAI,YAAY,KAAK,cAAc;AACjC,WAAK,4BAA4B;AACjC,WAAK,uBAAuB,KAAK,QAAQ,kBAAkB,MAAM,CAAC,sBAAsB,KAAK,WAAW,SAAS,aAAa,KAAK,cAAc,0BAA0B,GAAG,sBAAsB,KAAK,WAAW,SAAS,cAAc,KAAK,cAAc,2BAA2B,GAAG,sBAAsB,KAAK,WAAW,SAAS,aAAa,KAAK,kBAAkB,0BAA0B,CAAC,CAAC;AAC3Y,WAAK,oBAAoB;AACzB,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,OAAO,eAAe,eAAe,KAAK,wBAAwB,YAAY;AAChF,WAAK,mBAAmB,KAAK,aAAa;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB,iBAAiB;AACnC,SAAK,mBAAmB,kBAAkB,cAAc,eAAe,IAAI;AAC3E,SAAK,oBAAoB,YAAY;AACrC,QAAI,iBAAiB;AACnB,WAAK,sBAAsB,KAAK,eAAe,OAAO,EAAE,EAAE,UAAU,MAAM,KAAK,+BAA+B,CAAC;AAAA,IACjH;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,WAAW,QAAQ;AACjB,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,4BAA4B;AAGjC,QAAI,KAAK,WAAW,GAAG;AAGrB,WAAK,cAAc,OAAO;AAAA,IAC5B;AACA,SAAK,SAAS,OAAO;AACrB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,eAAe,IAAI;AAC1C,SAAK,iBAAiB;AACtB,SAAK,cAAc,SAAS;AAC5B,SAAK,QAAQ,SAAS;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,MAAM,SAAS;AACpB,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,QAAQ,SAAS;AACtB,SAAK,YAAY,SAAS;AAC1B,SAAK,WAAW,CAAC;AACjB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,iBAAiB;AACtB,SAAK,oBAAoB,YAAY;AACrC,SAAK,iBAAiB,MAAM;AAC5B,SAAK,mBAAmB,KAAK,eAAe,KAAK,mBAAmB,KAAK,uBAAuB,KAAK,mBAAmB,KAAK,UAAU,KAAK,iBAAiB;AAAA,EAC/J;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,oBAAoB,KAAK,KAAK,kBAAkB,WAAW,IAAI;AAAA,EAC7E;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,aAAa,MAAM,YAAY,KAAK,qBAAqB;AAC9D,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,oBAAoB;AAAA,MACvB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,QAAQ;AACpB,QAAI,CAAC,KAAK,iBAAiB,IAAI,MAAM,KAAK,KAAK,SAAS,QAAQ,MAAM,IAAI,IAAI;AAC5E,WAAK,iBAAiB,IAAI,MAAM;AAChC,mCAA6B,QAAQ,IAAI;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,QAAQ;AACnB,QAAI,KAAK,iBAAiB,IAAI,MAAM,GAAG;AACrC,WAAK,iBAAiB,OAAO,MAAM;AACnC,mCAA6B,QAAQ,KAAK,QAAQ;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,UAAM,WAAW,KAAK,WAAW,IAAI,KAAK,mBAAmB,KAAK;AAClE,WAAO;AAAA,MACL,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,OAAO;AACzB,SAAK,mBAAmB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,kBAAkB,IAAI,MAAM;AACjC,SAAK,kBAAkB,IAAI,MAAM;AACjC,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,2BAA2B,MAAM,GAAG,MAAM,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,+BAA+B;AAC7B,UAAM,WAAW,KAAK;AACtB,QAAI,YAAY,KAAK,gBAAgB;AACnC,WAAK,2BAA2B,KAAK,+BAA+B,QAAQ,GAAG,QAAQ;AAAA,IACzF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,yBAAyB,YAAY;AAC1C,SAAK,uBAAuB,YAAY;AACxC,SAAK,oBAAoB,YAAY;AACrC,SAAK,gCAAgC;AACrC,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,cAAc,OAAO;AAC1B,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,eAAe,KAAK,kBAAkB;AAAA,EAC7C;AAAA;AAAA,EAEA,eAAe,WAAS;AACtB,SAAK,cAAc,KAAK;AAExB,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,eAAe,KAAK,iBAAiB,KAAK;AAChD,UAAI,gBAAgB,CAAC,KAAK,iBAAiB,IAAI,YAAY,KAAK,CAAC,KAAK,UAAU;AAC9E,aAAK,wBAAwB,cAAc,KAAK;AAAA,MAClD;AAAA,IACF,WAAW,CAAC,KAAK,UAAU;AACzB,WAAK,wBAAwB,KAAK,cAAc,KAAK;AAAA,IACvD;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,WAAS;AACtB,UAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,QAAI,CAAC,KAAK,oBAAoB,GAAG;AAC/B,YAAM,YAAY,KAAK,IAAI,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAC3E,YAAM,YAAY,KAAK,IAAI,gBAAgB,IAAI,KAAK,sBAAsB,CAAC;AAC3E,YAAM,kBAAkB,YAAY,aAAa,KAAK,QAAQ;AAK9D,UAAI,iBAAiB;AACnB,cAAM,iBAAiB,KAAK,IAAI,KAAK,KAAK,iBAAiB,KAAK,mBAAmB,KAAK;AACxF,cAAM,YAAY,KAAK;AACvB,YAAI,CAAC,gBAAgB;AACnB,eAAK,iBAAiB,KAAK;AAC3B;AAAA,QACF;AAIA,YAAI,CAAC,aAAa,CAAC,UAAU,WAAW,KAAK,CAAC,UAAU,YAAY,GAAG;AAGrE,cAAI,MAAM,YAAY;AACpB,kBAAM,eAAe;AAAA,UACvB;AACA,eAAK,oBAAoB,IAAI,IAAI;AACjC,eAAK,QAAQ,IAAI,MAAM,KAAK,mBAAmB,KAAK,CAAC;AAAA,QACvD;AAAA,MACF;AACA;AAAA,IACF;AAIA,QAAI,MAAM,YAAY;AACpB,YAAM,eAAe;AAAA,IACvB;AACA,UAAM,6BAA6B,KAAK,+BAA+B,eAAe;AACtF,SAAK,YAAY;AACjB,SAAK,4BAA4B;AACjC,SAAK,6BAA6B,0BAA0B;AAC5D,QAAI,KAAK,gBAAgB;AACvB,WAAK,2BAA2B,4BAA4B,eAAe;AAAA,IAC7E,OAAO;AAGL,YAAM,SAAS,KAAK,oBAAoB,KAAK,kBAAkB,KAAK;AACpE,YAAM,kBAAkB,KAAK;AAC7B,sBAAgB,IAAI,2BAA2B,IAAI,OAAO,IAAI,KAAK,kBAAkB;AACrF,sBAAgB,IAAI,2BAA2B,IAAI,OAAO,IAAI,KAAK,kBAAkB;AACrF,WAAK,2BAA2B,gBAAgB,GAAG,gBAAgB,CAAC;AAAA,IACtE;AAIA,QAAI,KAAK,YAAY,UAAU,QAAQ;AACrC,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,YAAY,KAAK;AAAA,UACpB,QAAQ;AAAA,UACR,iBAAiB;AAAA,UACjB;AAAA,UACA,UAAU,KAAK,iBAAiB,0BAA0B;AAAA,UAC1D,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,WAAS;AACpB,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AAKtB,QAAI,CAAC,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAC5C;AAAA,IACF;AACA,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,aAAa,IAAI;AACxC,SAAK,8BAA8B;AACnC,QAAI,KAAK,UAAU;AACjB,WAAK,aAAa,MAAM,0BAA0B,KAAK;AAAA,IACzD;AACA,QAAI,CAAC,KAAK,oBAAoB,GAAG;AAC/B;AAAA,IACF;AACA,SAAK,SAAS,KAAK;AAAA,MACjB,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,QAAI,KAAK,gBAAgB;AAEvB,WAAK,eAAe,eAAe;AACnC,WAAK,6BAA6B,EAAE,KAAK,MAAM;AAC7C,aAAK,sBAAsB,KAAK;AAChC,aAAK,yBAAyB;AAC9B,aAAK,kBAAkB,aAAa,IAAI;AAAA,MAC1C,CAAC;AAAA,IACH,OAAO;AAIL,WAAK,kBAAkB,IAAI,KAAK,iBAAiB;AACjD,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,WAAK,kBAAkB,IAAI,KAAK,iBAAiB;AACjD,WAAK,QAAQ,IAAI,MAAM;AACrB,aAAK,MAAM,KAAK;AAAA,UACd,QAAQ;AAAA,UACR,UAAU,KAAK,iBAAiB,eAAe;AAAA,UAC/C,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,yBAAyB;AAC9B,WAAK,kBAAkB,aAAa,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,QAAI,aAAa,KAAK,GAAG;AACvB,WAAK,sBAAsB,KAAK,IAAI;AAAA,IACtC;AACA,SAAK,8BAA8B;AAEnC,UAAM,aAAa,KAAK,eAAe;AACvC,UAAM,gBAAgB,KAAK;AAC3B,QAAI,YAAY;AAGd,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,gCAAgC,sBAAsB,KAAK,WAAW,YAAY,eAAe,sBAAsB,6BAA6B;AAAA,MAC3J,CAAC;AAAA,IACH;AACA,QAAI,eAAe;AACjB,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,QAAQ;AACvB,YAAM,cAAc,KAAK,eAAe,KAAK,0BAA0B;AACvE,YAAM,SAAS,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,cAAc,OAAO,cAAc,eAAe,YAAY,oBAAoB,EAAE;AAEjJ,aAAO,aAAa,QAAQ,OAAO;AAGnC,WAAK,oBAAoB,QAAQ,MAAM,aAAa;AAGpD,WAAK,WAAW,IAAI,WAAW,KAAK,WAAW,KAAK,cAAc,KAAK,YAAY,KAAK,iBAAiB,KAAK,oBAAoB,MAAM,KAAK,gBAAgB,MAAM,KAAK,uBAAuB,KAAK,mBAAmB,KAAK,QAAQ,UAAU,KAAM,KAAK,SAAS;AAClQ,WAAK,SAAS,OAAO,KAAK,0BAA0B,QAAQ,UAAU,CAAC;AAIvE,uBAAiB,SAAS,OAAO,uBAAuB;AACxD,WAAK,UAAU,KAAK,YAAY,OAAO,aAAa,aAAa,OAAO,CAAC;AACzE,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,oBAAc,MAAM;AACpB,WAAK,oBAAoB;AACzB,WAAK,gBAAgB,cAAc,aAAa,IAAI;AAAA,IACtD,OAAO;AACL,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,WAAK,oBAAoB,KAAK,gBAAgB;AAAA,IAChD;AAGA,SAAK,iBAAiB,MAAM,gBAAgB,cAAc,qBAAqB,IAAI,CAAC,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,kBAAkB,OAAO;AAG/C,QAAI,KAAK,gBAAgB;AACvB,YAAM,gBAAgB;AAAA,IACxB;AACA,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,kBAAkB,aAAa,KAAK;AAC1C,UAAM,yBAAyB,CAAC,mBAAmB,MAAM,WAAW;AACpE,UAAM,cAAc,KAAK;AACzB,UAAM,SAAS,gBAAgB,KAAK;AACpC,UAAM,mBAAmB,CAAC,mBAAmB,KAAK,uBAAuB,KAAK,sBAAsB,0BAA0B,KAAK,IAAI;AACvI,UAAM,cAAc,kBAAkB,iCAAiC,KAAK,IAAI,gCAAgC,KAAK;AAOrH,QAAI,UAAU,OAAO,aAAa,MAAM,SAAS,aAAa;AAC5D,YAAM,eAAe;AAAA,IACvB;AAEA,QAAI,cAAc,0BAA0B,oBAAoB,aAAa;AAC3E;AAAA,IACF;AAIA,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,aAAa,YAAY;AAC/B,WAAK,2BAA2B,WAAW,2BAA2B;AACtE,iBAAW,0BAA0B;AAAA,IACvC;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,KAAK,SAAS;AAG3C,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,KAAK,aAAa,sBAAsB;AAC/D,SAAK,2BAA2B,KAAK,kBAAkB,YAAY,UAAU,KAAK,YAAY;AAC9F,SAAK,yBAAyB,KAAK,kBAAkB,UAAU,UAAU,KAAK,UAAU;AACxF,SAAK,sBAAsB,KAAK,kBAAkB,SAAS,KAAK,eAAe,CAAC,EAAE,UAAU,iBAAe,KAAK,gBAAgB,WAAW,CAAC;AAC5I,QAAI,KAAK,kBAAkB;AACzB,WAAK,gBAAgB,qBAAqB,KAAK,gBAAgB;AAAA,IACjE;AAIA,UAAM,kBAAkB,KAAK;AAC7B,SAAK,2BAA2B,mBAAmB,gBAAgB,YAAY,CAAC,gBAAgB,YAAY;AAAA,MAC1G,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI,KAAK,6BAA6B,KAAK,iBAAiB,kBAAkB,KAAK;AACnF,UAAM,kBAAkB,KAAK,wBAAwB,KAAK,4BAA4B,KAAK,0BAA0B,KAAK;AAC1H,SAAK,yBAAyB;AAAA,MAC5B,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,wCAAwC;AAAA,MAC3C,GAAG,gBAAgB;AAAA,MACnB,GAAG,gBAAgB;AAAA,IACrB;AACA,SAAK,iBAAiB,KAAK,IAAI;AAC/B,SAAK,kBAAkB,cAAc,MAAM,KAAK;AAAA,EAClD;AAAA;AAAA,EAEA,sBAAsB,OAAO;AAK3B,qBAAiB,KAAK,cAAc,MAAM,uBAAuB;AACjE,SAAK,QAAQ,WAAW,aAAa,KAAK,cAAc,KAAK,OAAO;AACpE,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,eAAe,KAAK,oBAAoB;AAEzF,SAAK,QAAQ,IAAI,MAAM;AACrB,YAAM,YAAY,KAAK;AACvB,YAAM,eAAe,UAAU,aAAa,IAAI;AAChD,YAAM,kBAAkB,KAAK,0BAA0B,KAAK;AAC5D,YAAM,WAAW,KAAK,iBAAiB,eAAe;AACtD,YAAM,yBAAyB,UAAU,iBAAiB,gBAAgB,GAAG,gBAAgB,CAAC;AAC9F,WAAK,MAAM,KAAK;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,QACA,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,WAAK,QAAQ,KAAK;AAAA,QAChB,MAAM;AAAA,QACN;AAAA,QACA,eAAe,KAAK;AAAA,QACpB;AAAA,QACA,mBAAmB,KAAK;AAAA,QACxB;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,MAAM,cAAc,KAAK,eAAe,KAAK,mBAAmB,wBAAwB,UAAU,iBAAiB,KAAK;AACvI,WAAK,iBAAiB,KAAK;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B;AAAA,IACzB;AAAA,IACA;AAAA,EACF,GAAG;AAAA,IACD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG;AAED,QAAI,eAAe,KAAK,kBAAkB,iCAAiC,MAAM,GAAG,CAAC;AAKrF,QAAI,CAAC,gBAAgB,KAAK,mBAAmB,KAAK,qBAAqB,KAAK,kBAAkB,iBAAiB,GAAG,CAAC,GAAG;AACpH,qBAAe,KAAK;AAAA,IACtB;AACA,QAAI,gBAAgB,iBAAiB,KAAK,gBAAgB;AACxD,WAAK,QAAQ,IAAI,MAAM;AAErB,aAAK,OAAO,KAAK;AAAA,UACf,MAAM;AAAA,UACN,WAAW,KAAK;AAAA,QAClB,CAAC;AACD,aAAK,eAAe,KAAK,IAAI;AAE7B,aAAK,iBAAiB;AACtB,aAAK,eAAe,MAAM,MAAM,GAAG,GAAG,iBAAiB,KAAK;AAAA;AAAA,QAG5D,aAAa,kBAAkB,KAAK,gBAAgB,MAAS;AAC7D,aAAK,QAAQ,KAAK;AAAA,UAChB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,cAAc,aAAa,aAAa,IAAI;AAAA,QAC9C,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,eAAe,2BAA2B,MAAM,IAAI;AACzD,WAAK,eAAe,UAAU,MAAM,GAAG,GAAG,KAAK,sBAAsB;AACrE,UAAI,KAAK,mBAAmB;AAC1B,aAAK,uBAAuB,GAAG,CAAC;AAAA,MAClC,OAAO;AACL,aAAK,uBAAuB,IAAI,KAAK,yBAAyB,GAAG,IAAI,KAAK,yBAAyB,CAAC;AAAA,MACtG;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,+BAA+B;AAE7B,QAAI,CAAC,KAAK,WAAW;AACnB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,UAAM,kBAAkB,KAAK,aAAa,sBAAsB;AAEhE,SAAK,SAAS,SAAS,oBAAoB;AAE3C,SAAK,uBAAuB,gBAAgB,MAAM,gBAAgB,GAAG;AAKrE,UAAM,WAAW,KAAK,SAAS,sBAAsB;AACrD,QAAI,aAAa,GAAG;AAClB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,aAAO,IAAI,QAAQ,aAAW;AAC5B,cAAM,UAAU,WAAS;AACvB,cAAI,CAAC,SAAS,KAAK,YAAY,gBAAgB,KAAK,MAAM,KAAK,SAAS,WAAW,MAAM,iBAAiB,aAAa;AACrH,4BAAgB;AAChB,oBAAQ;AACR,yBAAa,OAAO;AAAA,UACtB;AAAA,QACF;AAIA,cAAM,UAAU,WAAW,SAAS,WAAW,GAAG;AAClD,cAAM,kBAAkB,KAAK,SAAS,iBAAiB,iBAAiB,OAAO;AAAA,MACjF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,4BAA4B;AAC1B,UAAM,oBAAoB,KAAK;AAC/B,UAAM,sBAAsB,oBAAoB,kBAAkB,WAAW;AAC7E,QAAI;AACJ,QAAI,qBAAqB;AACvB,WAAK,kBAAkB,kBAAkB,cAAc,mBAAmB,qBAAqB,kBAAkB,OAAO;AACxH,WAAK,gBAAgB,cAAc;AACnC,oBAAc,YAAY,KAAK,iBAAiB,KAAK,SAAS;AAAA,IAChE,OAAO;AACL,oBAAc,cAAc,KAAK,YAAY;AAAA,IAC/C;AAGA,gBAAY,MAAM,gBAAgB;AAClC,gBAAY,UAAU,IAAI,sBAAsB;AAChD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,aAAa,kBAAkB,OAAO;AACjE,UAAM,gBAAgB,qBAAqB,KAAK,eAAe,OAAO;AACtE,UAAM,gBAAgB,gBAAgB,cAAc,sBAAsB,IAAI;AAC9E,UAAM,QAAQ,aAAa,KAAK,IAAI,MAAM,cAAc,CAAC,IAAI;AAC7D,UAAM,iBAAiB,KAAK,2BAA2B;AACvD,UAAM,IAAI,MAAM,QAAQ,cAAc,OAAO,eAAe;AAC5D,UAAM,IAAI,MAAM,QAAQ,cAAc,MAAM,eAAe;AAC3D,WAAO;AAAA,MACL,GAAG,cAAc,OAAO,YAAY,OAAO;AAAA,MAC3C,GAAG,cAAc,MAAM,YAAY,MAAM;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA,EAEA,0BAA0B,OAAO;AAC/B,UAAM,iBAAiB,KAAK,2BAA2B;AACvD,UAAM,QAAQ,aAAa,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQhC,MAAM,QAAQ,CAAC,KAAK,MAAM,eAAe,CAAC,KAAK;AAAA,QAC7C,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,QAAI;AACJ,UAAM,IAAI,MAAM,QAAQ,eAAe;AACvC,UAAM,IAAI,MAAM,QAAQ,eAAe;AAGvC,QAAI,KAAK,kBAAkB;AACzB,YAAM,YAAY,KAAK,iBAAiB,aAAa;AACrD,UAAI,WAAW;AACb,cAAM,WAAW,KAAK,iBAAiB,eAAe;AACtD,iBAAS,IAAI;AACb,iBAAS,IAAI;AACb,eAAO,SAAS,gBAAgB,UAAU,QAAQ,CAAC;AAAA,MACrD;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,+BAA+B,OAAO;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,KAAK,eAAe,WAAW;AAC/E,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK,oBAAoB,KAAK,kBAAkB,OAAO,MAAM,KAAK,iBAAiB,KAAK,wBAAwB,IAAI;AACxH,QAAI,KAAK,aAAa,OAAO,sBAAsB,KAAK;AACtD,UAAI,KAAK,sBAAsB,KAAK,KAAK,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,IACjG,WAAW,KAAK,aAAa,OAAO,sBAAsB,KAAK;AAC7D,UAAI,KAAK,sBAAsB,KAAK,KAAK,oBAAoB,KAAK,yBAAyB,IAAI;AAAA,IACjG;AACA,QAAI,KAAK,eAAe;AAGtB,YAAM;AAAA,QACJ,GAAG;AAAA,QACH,GAAG;AAAA,MACL,IAAI,CAAC,KAAK,oBAAoB,KAAK,2BAA2B;AAAA,QAC5D,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,YAAM,eAAe,KAAK;AAC1B,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,IAAI,KAAK,gBAAgB;AACzB,YAAM,OAAO,aAAa,MAAM;AAChC,YAAM,OAAO,aAAa,UAAU,gBAAgB;AACpD,YAAM,OAAO,aAAa,OAAO;AACjC,YAAM,OAAO,aAAa,SAAS,eAAe;AAClD,UAAI,QAAQ,GAAG,MAAM,IAAI;AACzB,UAAI,QAAQ,GAAG,MAAM,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,6BAA6B,uBAAuB;AAClD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,QAAQ,KAAK;AACnB,UAAM,0BAA0B,KAAK;AAErC,UAAM,UAAU,KAAK,IAAI,IAAI,wBAAwB,CAAC;AACtD,UAAM,UAAU,KAAK,IAAI,IAAI,wBAAwB,CAAC;AAKtD,QAAI,UAAU,KAAK,QAAQ,iCAAiC;AAC1D,YAAM,IAAI,IAAI,wBAAwB,IAAI,IAAI;AAC9C,8BAAwB,IAAI;AAAA,IAC9B;AACA,QAAI,UAAU,KAAK,QAAQ,iCAAiC;AAC1D,YAAM,IAAI,IAAI,wBAAwB,IAAI,IAAI;AAC9C,8BAAwB,IAAI;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gCAAgC;AAC9B,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,UAAU;AACxC;AAAA,IACF;AACA,UAAM,eAAe,KAAK,SAAS,SAAS,KAAK,CAAC,KAAK,WAAW;AAClE,QAAI,iBAAiB,KAAK,4BAA4B;AACpD,WAAK,6BAA6B;AAClC,mCAA6B,KAAK,cAAc,YAAY;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA,EAEA,8BAA8B;AAC5B,SAAK,sBAAsB,QAAQ,aAAW,QAAQ,CAAC;AACvD,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,2BAA2B,GAAG,GAAG;AAC/B,UAAM,QAAQ,IAAI,KAAK;AACvB,UAAM,YAAY,aAAa,IAAI,OAAO,IAAI,KAAK;AACnD,UAAM,SAAS,KAAK,aAAa;AAIjC,QAAI,KAAK,qBAAqB,MAAM;AAClC,WAAK,oBAAoB,OAAO,aAAa,OAAO,aAAa,SAAS,OAAO,YAAY;AAAA,IAC/F;AAIA,WAAO,YAAY,kBAAkB,WAAW,KAAK,iBAAiB;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,GAAG,GAAG;AAG3B,UAAM,mBAAmB,KAAK,kBAAkB,WAAW,SAAY,KAAK;AAC5E,UAAM,YAAY,aAAa,GAAG,CAAC;AACnC,SAAK,SAAS,aAAa,kBAAkB,WAAW,gBAAgB,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,iBAAiB;AAChC,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,aAAO;AAAA,QACL,GAAG,gBAAgB,IAAI,eAAe;AAAA,QACtC,GAAG,gBAAgB,IAAI,eAAe;AAAA,MACxC;AAAA,IACF;AACA,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B;AACzB,SAAK,gBAAgB,KAAK,eAAe;AACzC,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iCAAiC;AAC/B,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,MAAM,KAAK,MAAM,KAAK,KAAK,WAAW,KAAK,CAAC,KAAK,kBAAkB;AACrE;AAAA,IACF;AAEA,UAAM,cAAc,KAAK,aAAa,sBAAsB;AAC5D,UAAM,eAAe,KAAK,iBAAiB,sBAAsB;AAGjE,QAAI,aAAa,UAAU,KAAK,aAAa,WAAW,KAAK,YAAY,UAAU,KAAK,YAAY,WAAW,GAAG;AAChH;AAAA,IACF;AACA,UAAM,eAAe,aAAa,OAAO,YAAY;AACrD,UAAM,gBAAgB,YAAY,QAAQ,aAAa;AACvD,UAAM,cAAc,aAAa,MAAM,YAAY;AACnD,UAAM,iBAAiB,YAAY,SAAS,aAAa;AAGzD,QAAI,aAAa,QAAQ,YAAY,OAAO;AAC1C,UAAI,eAAe,GAAG;AACpB,aAAK;AAAA,MACP;AACA,UAAI,gBAAgB,GAAG;AACrB,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,UAAI;AAAA,IACN;AAGA,QAAI,aAAa,SAAS,YAAY,QAAQ;AAC5C,UAAI,cAAc,GAAG;AACnB,aAAK;AAAA,MACP;AACA,UAAI,iBAAiB,GAAG;AACtB,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,UAAI;AAAA,IACN;AACA,QAAI,MAAM,KAAK,kBAAkB,KAAK,MAAM,KAAK,kBAAkB,GAAG;AACpE,WAAK,oBAAoB;AAAA,QACvB;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,UAAM,QAAQ,KAAK;AACnB,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT,WAAW,aAAa,KAAK,GAAG;AAC9B,aAAO,MAAM;AAAA,IACf;AACA,WAAO,QAAQ,MAAM,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,UAAM,mBAAmB,KAAK,iBAAiB,aAAa,KAAK;AACjE,QAAI,kBAAkB;AACpB,YAAM,SAAS,gBAAgB,KAAK;AAGpC,UAAI,KAAK,iBAAiB,WAAW,KAAK,oBAAoB,OAAO,SAAS,KAAK,gBAAgB,GAAG;AACpG,sBAAc,KAAK,eAAe,iBAAiB,KAAK,iBAAiB,IAAI;AAAA,MAC/E;AACA,WAAK,sBAAsB,KAAK,iBAAiB;AACjD,WAAK,sBAAsB,KAAK,iBAAiB;AAGjD,UAAI,CAAC,KAAK,gBAAgB;AACxB,aAAK,iBAAiB,KAAK,iBAAiB;AAC5C,aAAK,iBAAiB,KAAK,iBAAiB;AAC5C,aAAK,2BAA2B,KAAK,iBAAiB,GAAG,KAAK,iBAAiB,CAAC;AAAA,MAClF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,6BAA6B;AAC3B,WAAO,KAAK,iBAAiB,UAAU,IAAI,KAAK,SAAS,GAAG,kBAAkB,KAAK,iBAAiB,0BAA0B;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,KAAK,sBAAsB,QAAW;AACxC,WAAK,oBAAoB,eAAe,KAAK,YAAY;AAAA,IAC3D;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,0BAA0B,eAAe,YAAY;AACnD,UAAM,mBAAmB,KAAK,qBAAqB;AACnD,QAAI,qBAAqB,UAAU;AACjC,aAAO;AAAA,IACT;AACA,QAAI,qBAAqB,UAAU;AACjC,YAAM,cAAc,KAAK;AAIzB,aAAO,cAAc,YAAY,qBAAqB,YAAY,2BAA2B,YAAY,wBAAwB,YAAY,uBAAuB,YAAY;AAAA,IAClL;AACA,WAAO,cAAc,gBAAgB;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB;AAGhB,QAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,SAAS,CAAC,KAAK,aAAa,QAAQ;AAC/E,WAAK,eAAe,KAAK,WAAW,KAAK,SAAS,sBAAsB,IAAI,KAAK;AAAA,IACnF;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,mBAAmB,WAAS;AAC1B,QAAI,KAAK,SAAS,QAAQ;AACxB,YAAM,eAAe,KAAK,iBAAiB,KAAK;AAChD,UAAI,gBAAgB,CAAC,KAAK,iBAAiB,IAAI,YAAY,KAAK,CAAC,KAAK,UAAU;AAC9E,cAAM,eAAe;AAAA,MACvB;AAAA,IACF,WAAW,CAAC,KAAK,UAAU;AAGzB,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,WAAO,KAAK,SAAS,KAAK,YAAU;AAClC,aAAO,MAAM,WAAW,MAAM,WAAW,UAAU,OAAO,SAAS,MAAM,MAAM;AAAA,IACjF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,QAAQ,OAAO,KAAK,KAAK;AAChC,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;AAEA,SAAS,aAAa,OAAO;AAI3B,SAAO,MAAM,KAAK,CAAC,MAAM;AAC3B;AAEA,SAAS,qBAAqB,OAAO;AACnC,QAAM,eAAe;AACvB;AAQA,SAAS,gBAAgB,OAAO,WAAW,SAAS;AAClD,QAAM,OAAO,MAAM,WAAW,MAAM,SAAS,CAAC;AAC9C,QAAM,KAAK,MAAM,SAAS,MAAM,SAAS,CAAC;AAC1C,MAAI,SAAS,IAAI;AACf;AAAA,EACF;AACA,QAAM,SAAS,MAAM,IAAI;AACzB,QAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,WAAS,IAAI,MAAM,MAAM,IAAI,KAAK,OAAO;AACvC,UAAM,CAAC,IAAI,MAAM,IAAI,KAAK;AAAA,EAC5B;AACA,QAAM,EAAE,IAAI;AACd;AAQA,SAAS,kBAAkB,cAAc,aAAa,cAAc,aAAa;AAC/E,QAAM,OAAO,MAAM,cAAc,aAAa,SAAS,CAAC;AACxD,QAAM,KAAK,MAAM,aAAa,YAAY,MAAM;AAChD,MAAI,aAAa,QAAQ;AACvB,gBAAY,OAAO,IAAI,GAAG,aAAa,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC;AAAA,EAC3D;AACF;AAUA,SAAS,cAAc,cAAc,aAAa,cAAc,aAAa;AAC3E,QAAM,KAAK,MAAM,aAAa,YAAY,MAAM;AAChD,MAAI,aAAa,QAAQ;AACvB,gBAAY,OAAO,IAAI,GAAG,aAAa,YAAY,CAAC;AAAA,EACtD;AACF;AAEA,SAAS,MAAM,OAAO,KAAK;AACzB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC;AACzC;AAOA,IAAM,yBAAN,MAA6B;AAAA,EAC3B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA,EAEd;AAAA,EACA,YAAY,mBAAmB;AAC7B,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,MAAM,UAAU,UAAU,cAAc;AAC3C,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,KAAK,iCAAiC,MAAM,UAAU,UAAU,YAAY;AAC7F,QAAI,aAAa,MAAM,SAAS,SAAS,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,eAAe,SAAS,UAAU,iBAAe,YAAY,SAAS,IAAI;AAChF,UAAM,uBAAuB,SAAS,QAAQ;AAC9C,UAAM,kBAAkB,SAAS,YAAY,EAAE;AAC/C,UAAM,cAAc,qBAAqB;AACzC,UAAM,QAAQ,eAAe,WAAW,IAAI;AAE5C,UAAM,aAAa,KAAK,iBAAiB,iBAAiB,aAAa,KAAK;AAE5E,UAAM,gBAAgB,KAAK,oBAAoB,cAAc,UAAU,KAAK;AAG5E,UAAM,WAAW,SAAS,MAAM;AAEhC,oBAAgB,UAAU,cAAc,QAAQ;AAChD,aAAS,QAAQ,CAAC,SAAS,UAAU;AAEnC,UAAI,SAAS,KAAK,MAAM,SAAS;AAC/B;AAAA,MACF;AACA,YAAM,gBAAgB,QAAQ,SAAS;AACvC,YAAM,SAAS,gBAAgB,aAAa;AAC5C,YAAM,kBAAkB,gBAAgB,KAAK,sBAAsB,IAAI,QAAQ,KAAK,eAAe;AAEnG,cAAQ,UAAU;AAClB,YAAM,kBAAkB,KAAK,MAAM,QAAQ,UAAU,IAAI,QAAQ,KAAK,MAAM;AAK5E,UAAI,cAAc;AAGhB,wBAAgB,MAAM,YAAY,kBAAkB,eAAe,eAAe,aAAa,QAAQ,gBAAgB;AACvH,sBAAc,QAAQ,YAAY,GAAG,MAAM;AAAA,MAC7C,OAAO;AACL,wBAAgB,MAAM,YAAY,kBAAkB,kBAAkB,eAAe,UAAU,QAAQ,gBAAgB;AACvH,sBAAc,QAAQ,YAAY,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACF,CAAC;AAED,SAAK,cAAc,WAAW,mBAAmB,aAAa,UAAU,QAAQ;AAChF,SAAK,cAAc,OAAO,qBAAqB;AAC/C,SAAK,cAAc,QAAQ,eAAe,aAAa,IAAI,aAAa;AACxE,WAAO;AAAA,MACL,eAAe;AAAA,MACf,cAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,UAAM,WAAW,SAAS,QAAQ,QAAQ;AAAA;AAAA;AAAA,MAG1C,KAAK,iCAAiC,MAAM,UAAU,QAAQ;AAAA,QAAI;AAClE,UAAM,mBAAmB,KAAK;AAC9B,UAAM,eAAe,iBAAiB,QAAQ,IAAI;AAClD,UAAM,cAAc,KAAK,sBAAsB;AAC/C,QAAI,uBAAuB,iBAAiB,QAAQ;AAIpD,QAAI,yBAAyB,MAAM;AACjC,6BAAuB,iBAAiB,WAAW,CAAC;AAAA,IACtD;AAGA,QAAI,CAAC,yBAAyB,YAAY,QAAQ,aAAa,MAAM,WAAW,iBAAiB,SAAS,MAAM,KAAK,yBAAyB,UAAU,QAAQ,GAAG;AACjK,6BAAuB,iBAAiB,CAAC;AAAA,IAC3C;AAGA,QAAI,eAAe,IAAI;AACrB,uBAAiB,OAAO,cAAc,CAAC;AAAA,IACzC;AAGA,QAAI,wBAAwB,CAAC,KAAK,kBAAkB,WAAW,oBAAoB,GAAG;AACpF,YAAM,UAAU,qBAAqB,eAAe;AACpD,cAAQ,cAAc,aAAa,aAAa,OAAO;AACvD,uBAAiB,OAAO,UAAU,GAAG,IAAI;AAAA,IAC3C,OAAO;AACL,WAAK,SAAS,YAAY,WAAW;AACrC,uBAAiB,KAAK,IAAI;AAAA,IAC5B;AAEA,gBAAY,MAAM,YAAY;AAI9B,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,SAAK,oBAAoB,MAAM,MAAM;AACrC,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA,EAEA,kBAAkB,WAAW;AAC3B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AAEN,SAAK,mBAAmB,QAAQ,UAAQ;AACtC,YAAM,cAAc,KAAK,eAAe;AACxC,UAAI,aAAa;AACf,cAAM,mBAAmB,KAAK,eAAe,KAAK,OAAK,EAAE,SAAS,IAAI,GAAG;AACzE,oBAAY,MAAM,YAAY,oBAAoB;AAAA,MACpD;AAAA,IACF,CAAC;AACD,SAAK,iBAAiB,CAAC;AACvB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,cAAc,OAAO;AAC1B,SAAK,cAAc,QAAQ;AAC3B,SAAK,cAAc,WAAW;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa,MAAM;AAIjB,UAAM,QAAQ,KAAK,gBAAgB,gBAAgB,KAAK,cAAc,QAAQ,KAAK,eAAe,MAAM,EAAE,QAAQ,IAAI,KAAK;AAC3H,WAAO,MAAM,UAAU,iBAAe,YAAY,SAAS,IAAI;AAAA,EACjE;AAAA;AAAA,EAEA,eAAe,eAAe,gBAAgB;AAK5C,SAAK,eAAe,QAAQ,CAAC;AAAA,MAC3B;AAAA,IACF,MAAM;AACJ,oBAAc,YAAY,eAAe,cAAc;AAAA,IACzD,CAAC;AAGD,SAAK,eAAe,QAAQ,CAAC;AAAA,MAC3B;AAAA,IACF,MAAM;AACJ,UAAI,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAG3C,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,WAAW;AAC9B,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,SAAK,iBAAiB,KAAK,kBAAkB,IAAI,UAAQ;AACvD,YAAM,mBAAmB,KAAK,kBAAkB;AAChD,aAAO;AAAA,QACL;AAAA,QACA,QAAQ;AAAA,QACR,kBAAkB,iBAAiB,MAAM,aAAa;AAAA,QACtD,YAAY,qBAAqB,gBAAgB;AAAA,MACnD;AAAA,IACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAChB,aAAO,eAAe,EAAE,WAAW,OAAO,EAAE,WAAW,OAAO,EAAE,WAAW,MAAM,EAAE,WAAW;AAAA,IAChG,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,iBAAiB,aAAa,OAAO;AACpD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,aAAa,eAAe,YAAY,OAAO,gBAAgB,OAAO,YAAY,MAAM,gBAAgB;AAE5G,QAAI,UAAU,IAAI;AAChB,oBAAc,eAAe,YAAY,QAAQ,gBAAgB,QAAQ,YAAY,SAAS,gBAAgB;AAAA,IAChH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,cAAc,UAAU,OAAO;AACjD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,kBAAkB,SAAS,YAAY,EAAE;AAC/C,UAAM,mBAAmB,SAAS,eAAe,QAAQ,EAAE;AAC3D,QAAI,gBAAgB,gBAAgB,eAAe,UAAU,QAAQ,IAAI;AACzE,QAAI,kBAAkB;AACpB,YAAM,QAAQ,eAAe,SAAS;AACtC,YAAM,MAAM,eAAe,UAAU;AAKrC,UAAI,UAAU,IAAI;AAChB,yBAAiB,iBAAiB,WAAW,KAAK,IAAI,gBAAgB,GAAG;AAAA,MAC3E,OAAO;AACL,yBAAiB,gBAAgB,KAAK,IAAI,iBAAiB,WAAW,GAAG;AAAA,MAC3E;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,UAAU,UAAU;AAC3C,QAAI,CAAC,KAAK,kBAAkB,QAAQ;AAClC,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,KAAK;AAC3B,UAAM,eAAe,KAAK,gBAAgB;AAG1C,UAAM,WAAW,cAAc,CAAC,EAAE,SAAS,KAAK,kBAAkB,CAAC;AACnE,QAAI,UAAU;AACZ,YAAM,eAAe,cAAc,cAAc,SAAS,CAAC,EAAE;AAC7D,aAAO,eAAe,YAAY,aAAa,QAAQ,YAAY,aAAa;AAAA,IAClF,OAAO;AACL,YAAM,gBAAgB,cAAc,CAAC,EAAE;AACvC,aAAO,eAAe,YAAY,cAAc,OAAO,YAAY,cAAc;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,UAAU,UAAU,OAAO;AAChE,UAAM,eAAe,KAAK,gBAAgB;AAC1C,UAAM,QAAQ,KAAK,eAAe,UAAU,CAAC;AAAA,MAC3C;AAAA,MACA;AAAA,IACF,MAAM;AAEJ,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AACA,UAAI,OAAO;AACT,cAAM,YAAY,eAAe,MAAM,IAAI,MAAM;AAIjD,YAAI,SAAS,KAAK,cAAc,QAAQ,KAAK,cAAc,YAAY,cAAc,KAAK,cAAc,OAAO;AAC7G,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA;AAAA;AAAA,QAGP,YAAY,KAAK,MAAM,WAAW,IAAI,KAAK,WAAW,KAAK,MAAM,WAAW,KAAK;AAAA,UAAI,YAAY,KAAK,MAAM,WAAW,GAAG,KAAK,WAAW,KAAK,MAAM,WAAW,MAAM;AAAA,IACxK,CAAC;AACD,WAAO,UAAU,MAAM,CAAC,KAAK,eAAe,OAAO,IAAI,IAAI,KAAK;AAAA,EAClE;AACF;AAOA,IAAM,oBAAN,MAAwB;AAAA,EACtB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,CAAC;AAAA,EACjB,YAAY,WAAW,mBAAmB;AACxC,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,UAAM,aAAa,KAAK,SAAS;AACjC,SAAK,gBAAgB,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,YAAM,OAAO,WAAW,CAAC;AACzB,WAAK,cAAc,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC;AAAA,IAClD;AACA,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,MAAM,UAAU,UAAU,cAAc;AAC3C,UAAM,WAAW,KAAK,iCAAiC,MAAM,UAAU,QAAQ;AAC/E,UAAM,eAAe,KAAK;AAC1B,QAAI,aAAa,MAAM,KAAK,aAAa,QAAQ,MAAM,MAAM;AAC3D,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK,aAAa,QAAQ;AAE7C,QAAI,aAAa,SAAS,cAAc,aAAa,YAAY,aAAa,WAAW,aAAa,KAAK,aAAa,WAAW,aAAa,GAAG;AACjJ,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,KAAK,aAAa,IAAI;AAC5C,UAAM,UAAU,KAAK,sBAAsB;AAC3C,UAAM,iBAAiB,WAAW,eAAe;AACjD,QAAI,WAAW,eAAe;AAC5B,qBAAe,MAAM,OAAO;AAAA,IAC9B,OAAO;AACL,qBAAe,OAAO,OAAO;AAAA,IAC/B;AACA,oBAAgB,KAAK,cAAc,eAAe,QAAQ;AAC1D,UAAM,oBAAoB,KAAK,aAAa,EAAE,iBAAiB,UAAU,QAAQ;AAGjF,iBAAa,SAAS,aAAa;AACnC,iBAAa,SAAS,aAAa;AACnC,iBAAa,OAAO;AACpB,iBAAa,WAAW,mBAAmB,qBAAqB,eAAe,SAAS,iBAAiB;AACzG,WAAO;AAAA,MACL;AAAA,MACA,cAAc;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,QAAI,aAAa,SAAS,QAAQ,QAAQ,IAAI,KAAK,iCAAiC,MAAM,UAAU,QAAQ,IAAI;AAIhH,QAAI,eAAe,IAAI;AACrB,mBAAa,KAAK,8BAA8B,MAAM,UAAU,QAAQ;AAAA,IAC1E;AACA,UAAM,aAAa,KAAK,aAAa,UAAU;AAC/C,UAAM,eAAe,KAAK,aAAa,QAAQ,IAAI;AACnD,QAAI,eAAe,IAAI;AACrB,WAAK,aAAa,OAAO,cAAc,CAAC;AAAA,IAC1C;AACA,QAAI,cAAc,CAAC,KAAK,kBAAkB,WAAW,UAAU,GAAG;AAChE,WAAK,aAAa,OAAO,YAAY,GAAG,IAAI;AAC5C,iBAAW,eAAe,EAAE,OAAO,KAAK,sBAAsB,CAAC;AAAA,IACjE,OAAO;AACL,WAAK,aAAa,KAAK,IAAI;AAC3B,WAAK,SAAS,YAAY,KAAK,sBAAsB,CAAC;AAAA,IACxD;AAAA,EACF;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,SAAK,eAAe,MAAM,MAAM;AAAA,EAClC;AAAA;AAAA,EAEA,kBAAkB,WAAW;AAC3B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AACN,UAAM,OAAO,KAAK;AAClB,UAAM,eAAe,KAAK;AAQ1B,aAAS,IAAI,KAAK,cAAc,SAAS,GAAG,IAAI,IAAI,KAAK;AACvD,YAAM,CAAC,MAAM,WAAW,IAAI,KAAK,cAAc,CAAC;AAChD,UAAI,KAAK,eAAe,QAAQ,KAAK,gBAAgB,aAAa;AAChE,YAAI,gBAAgB,MAAM;AACxB,eAAK,YAAY,IAAI;AAAA,QACvB,WAAW,YAAY,eAAe,MAAM;AAC1C,eAAK,aAAa,MAAM,WAAW;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AACA,SAAK,gBAAgB,CAAC;AACtB,SAAK,eAAe,CAAC;AACrB,iBAAa,OAAO;AACpB,iBAAa,SAAS,aAAa,SAAS;AAC5C,iBAAa,WAAW;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,WAAO,KAAK,aAAa,QAAQ,IAAI;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,aAAa,QAAQ,UAAQ;AAChC,UAAI,KAAK,kBAAkB,WAAW,IAAI,GAAG;AAG3C,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB,WAAW;AAC9B,QAAI,cAAc,KAAK,UAAU;AAC/B,WAAK,WAAW;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,UAAU,UAAU;AACzD,UAAM,iBAAiB,KAAK,aAAa,EAAE,iBAAiB,KAAK,MAAM,QAAQ,GAAG,KAAK,MAAM,QAAQ,CAAC;AACtG,UAAM,QAAQ,iBAAiB,KAAK,aAAa,UAAU,CAAAC,UAAQ;AACjE,YAAM,OAAOA,MAAK,eAAe;AACjC,aAAO,mBAAmB,QAAQ,KAAK,SAAS,cAAc;AAAA,IAChE,CAAC,IAAI;AACL,WAAO,UAAU,MAAM,CAAC,KAAK,eAAe,OAAO,IAAI,IAAI,KAAK;AAAA,EAClE;AAAA;AAAA,EAEA,eAAe;AAEb,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY,eAAe,KAAK,QAAQ,KAAK,KAAK;AAAA,IACzD;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B,MAAM,UAAU,UAAU;AACtD,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,QAAI,cAAc;AAClB,QAAI,WAAW;AAKf,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACjD,YAAM,UAAU,KAAK,aAAa,CAAC;AACnC,UAAI,YAAY,MAAM;AACpB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,QAAQ,eAAe,EAAE,sBAAsB;AACnD,cAAM,WAAW,KAAK,MAAM,WAAW,GAAG,WAAW,CAAC;AACtD,YAAI,WAAW,aAAa;AAC1B,wBAAc;AACd,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAMA,IAAM,2BAA2B;AAKjC,IAAM,6BAA6B;AAEnC,IAAI;AAAA,CACH,SAAUC,8BAA6B;AACtC,EAAAA,6BAA4BA,6BAA4B,MAAM,IAAI,CAAC,IAAI;AACvE,EAAAA,6BAA4BA,6BAA4B,IAAI,IAAI,CAAC,IAAI;AACrE,EAAAA,6BAA4BA,6BAA4B,MAAM,IAAI,CAAC,IAAI;AACzE,GAAG,gCAAgC,8BAA8B,CAAC,EAAE;AAEpE,IAAI;AAAA,CACH,SAAUC,gCAA+B;AACxC,EAAAA,+BAA8BA,+BAA8B,MAAM,IAAI,CAAC,IAAI;AAC3E,EAAAA,+BAA8BA,+BAA8B,MAAM,IAAI,CAAC,IAAI;AAC3E,EAAAA,+BAA8BA,+BAA8B,OAAO,IAAI,CAAC,IAAI;AAC9E,GAAG,kCAAkC,gCAAgC,CAAC,EAAE;AAIxE,IAAM,cAAN,MAAkB;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA,EAEX,kBAAkB;AAAA;AAAA,EAElB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAAA;AAAA,EAErB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,iBAAiB,MAAM;AAAA;AAAA,EAEvB,gBAAgB,MAAM;AAAA;AAAA,EAEtB,gBAAgB,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI5B,UAAU,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,UAAU,IAAI,QAAQ;AAAA;AAAA,EAEtB,SAAS,IAAI,QAAQ;AAAA;AAAA,EAErB,mBAAmB,IAAI,QAAQ;AAAA;AAAA,EAE/B,mBAAmB,IAAI,QAAQ;AAAA;AAAA,EAE/B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA,EAEd;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,cAAc,CAAC;AAAA;AAAA,EAEf,YAAY,CAAC;AAAA;AAAA,EAEb,kBAAkB,oBAAI,IAAI;AAAA;AAAA,EAE1B,8BAA8B,aAAa;AAAA;AAAA,EAE3C,2BAA2B,4BAA4B;AAAA;AAAA,EAEvD,6BAA6B,8BAA8B;AAAA;AAAA,EAE3D;AAAA;AAAA,EAEA,oBAAoB,IAAI,QAAQ;AAAA;AAAA,EAEhC,oBAAoB;AAAA;AAAA,EAEpB;AAAA;AAAA,EAEA,sBAAsB,CAAC;AAAA;AAAA,EAEvB;AAAA;AAAA,EAEA,aAAa;AAAA,EACb,YAAY,SAAS,mBAAmB,WAAW,SAAS,gBAAgB;AAC1E,SAAK,oBAAoB;AACzB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,UAAM,iBAAiB,KAAK,UAAU,cAAc,OAAO;AAC3D,SAAK,YAAY;AACjB,SAAK,gBAAgB,UAAU,EAAE,qBAAqB,cAAc;AACpE,sBAAkB,sBAAsB,IAAI;AAC5C,SAAK,mBAAmB,IAAI,sBAAsB,SAAS;AAAA,EAC7D;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe;AACpB,SAAK,kBAAkB,SAAS;AAChC,SAAK,4BAA4B,YAAY;AAC7C,SAAK,cAAc,SAAS;AAC5B,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,QAAQ,SAAS;AACtB,SAAK,OAAO,SAAS;AACrB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,iBAAiB,SAAS;AAC/B,SAAK,gBAAgB,MAAM;AAC3B,SAAK,cAAc;AACnB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,kBAAkB,oBAAoB,IAAI;AAAA,EACjD;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,iBAAiB;AACtB,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,MAAM,UAAU,UAAU,OAAO;AACrC,SAAK,iBAAiB;AAGtB,QAAI,SAAS,QAAQ,KAAK,iBAAiB;AACzC,cAAQ,KAAK,YAAY,QAAQ,IAAI;AAAA,IACvC;AACA,SAAK,cAAc,MAAM,MAAM,UAAU,UAAU,KAAK;AAGxD,SAAK,sBAAsB;AAE3B,SAAK,yBAAyB;AAC9B,SAAK,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,WAAW;AAAA,MACX,cAAc,KAAK,aAAa,IAAI;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AACT,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,KAAK,MAAM,cAAc,eAAe,mBAAmB,wBAAwB,UAAU,WAAW,QAAQ,CAAC,GAAG;AAClH,SAAK,OAAO;AACZ,SAAK,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,gBAAgB,KAAK;AAC3B,SAAK,cAAc;AACnB,UAAM,QAAQ,UAAQ,KAAK,mBAAmB,IAAI,CAAC;AACnD,QAAI,KAAK,WAAW,GAAG;AACrB,YAAM,eAAe,cAAc,OAAO,UAAQ,KAAK,WAAW,CAAC;AAGnE,UAAI,aAAa,MAAM,UAAQ,MAAM,QAAQ,IAAI,MAAM,EAAE,GAAG;AAC1D,aAAK,OAAO;AAAA,MACd,OAAO;AACL,aAAK,cAAc,UAAU,KAAK,WAAW;AAAA,MAC/C;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,SAAK,aAAa;AAClB,QAAI,KAAK,yBAAyB,wBAAwB;AACxD,WAAK,cAAc,YAAY;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,aAAa;AACvB,SAAK,YAAY,YAAY,MAAM;AACnC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,aAAa;AAC3B,QAAI,gBAAgB,SAAS;AAC3B,WAAK,gBAAgB,IAAI,kBAAkB,KAAK,WAAW,KAAK,iBAAiB;AAAA,IACnF,OAAO;AACL,YAAM,WAAW,IAAI,uBAAuB,KAAK,iBAAiB;AAClE,eAAS,YAAY,KAAK;AAC1B,eAAS,cAAc;AACvB,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,cAAc,qBAAqB,KAAK,UAAU;AACvD,SAAK,cAAc,kBAAkB,CAAC,OAAO,SAAS,KAAK,cAAc,OAAO,MAAM,IAAI,CAAC;AAC3F,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,UAAU;AAC9B,UAAM,UAAU,KAAK;AAGrB,SAAK,sBAAsB,SAAS,QAAQ,OAAO,MAAM,KAAK,CAAC,SAAS,GAAG,QAAQ,IAAI,SAAS,MAAM;AACtG,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,WAAW;AAC9B,QAAI,cAAc,KAAK,YAAY;AACjC,aAAO;AAAA,IACT;AACA,UAAM,UAAU,cAAc,KAAK,OAAO;AAC1C,SAAK,OAAO,cAAc,eAAe,cAAc,cAAc,WAAW,CAAC,QAAQ,SAAS,SAAS,GAAG;AAC5G,YAAM,IAAI,MAAM,yGAAyG;AAAA,IAC3H;AACA,UAAM,oBAAoB,KAAK,oBAAoB,QAAQ,KAAK,UAAU;AAC1E,UAAM,oBAAoB,KAAK,oBAAoB,QAAQ,SAAS;AACpE,QAAI,oBAAoB,IAAI;AAC1B,WAAK,oBAAoB,OAAO,mBAAmB,CAAC;AAAA,IACtD;AACA,QAAI,oBAAoB,IAAI;AAC1B,WAAK,oBAAoB,OAAO,mBAAmB,CAAC;AAAA,IACtD;AACA,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,qBAAqB,SAAS;AAAA,IACnD;AACA,SAAK,oBAAoB;AACzB,SAAK,oBAAoB,QAAQ,SAAS;AAC1C,SAAK,aAAa;AAClB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AACjB,WAAO,KAAK,cAAc,KAAK,cAAc,aAAa,IAAI,IAAI,KAAK,YAAY,QAAQ,IAAI;AAAA,EACjG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK,gBAAgB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,MAAM,UAAU,UAAU,cAAc;AAEhD,QAAI,KAAK,mBAAmB,CAAC,KAAK,YAAY,CAAC,qBAAqB,KAAK,UAAU,0BAA0B,UAAU,QAAQ,GAAG;AAChI;AAAA,IACF;AACA,UAAM,SAAS,KAAK,cAAc,KAAK,MAAM,UAAU,UAAU,YAAY;AAC7E,QAAI,QAAQ;AACV,WAAK,OAAO,KAAK;AAAA,QACf,eAAe,OAAO;AAAA,QACtB,cAAc,OAAO;AAAA,QACrB,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B,UAAU,UAAU;AAC7C,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,QAAI;AACJ,QAAI,0BAA0B,4BAA4B;AAC1D,QAAI,4BAA4B,8BAA8B;AAE9D,SAAK,iBAAiB,UAAU,QAAQ,CAAC,UAAU,YAAY;AAG7D,UAAI,YAAY,KAAK,aAAa,CAAC,SAAS,cAAc,YAAY;AACpE;AAAA,MACF;AACA,UAAI,qBAAqB,SAAS,YAAY,0BAA0B,UAAU,QAAQ,GAAG;AAC3F,SAAC,yBAAyB,yBAAyB,IAAI,2BAA2B,SAAS,SAAS,YAAY,KAAK,YAAY,UAAU,QAAQ;AACnJ,YAAI,2BAA2B,2BAA2B;AACxD,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,CAAC,2BAA2B,CAAC,2BAA2B;AAC1D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,eAAe,gBAAgB;AACxC,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,gCAA0B,2BAA2B,SAAS,QAAQ;AACtE,kCAA4B,6BAA6B,SAAS,QAAQ;AAC1E,mBAAa;AAAA,IACf;AACA,QAAI,eAAe,4BAA4B,KAAK,4BAA4B,8BAA8B,KAAK,8BAA8B,eAAe,KAAK,cAAc;AACjL,WAAK,2BAA2B;AAChC,WAAK,6BAA6B;AAClC,WAAK,cAAc;AACnB,WAAK,2BAA2B,8BAA8B,YAAY;AACxE,aAAK,QAAQ,kBAAkB,KAAK,oBAAoB;AAAA,MAC1D,OAAO;AACL,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA;AAAA,EAEA,mBAAmB;AACjB,UAAM,SAAS,KAAK,WAAW;AAC/B,SAAK,cAAc,KAAK;AACxB,SAAK,cAAc;AACnB,SAAK,OAAO,cAAc,eAAe;AAAA;AAAA,IAGzC,KAAK,eAAe,cAAc,KAAK,OAAO,GAAG;AAC/C,iBAAW,QAAQ,KAAK,aAAa;AACnC,YAAI,CAAC,KAAK,WAAW,KAAK,KAAK,kBAAkB,EAAE,eAAe,KAAK,YAAY;AACjF,gBAAM,IAAI,MAAM,yGAAyG;AAAA,QAC3H;AAAA,MACF;AAAA,IACF;AAIA,SAAK,qBAAqB,OAAO,oBAAoB,OAAO,kBAAkB;AAC9E,WAAO,iBAAiB,OAAO,mBAAmB;AAClD,SAAK,cAAc,MAAM,KAAK,WAAW;AACzC,SAAK,sBAAsB;AAC3B,SAAK,4BAA4B,YAAY;AAC7C,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,iBAAiB,MAAM,KAAK,mBAAmB;AAGpD,SAAK,WAAW,KAAK,iBAAiB,UAAU,IAAI,KAAK,UAAU,EAAE;AAAA,EACvE;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,cAAc;AACnB,UAAM,SAAS,KAAK,WAAW;AAC/B,WAAO,iBAAiB,OAAO,mBAAmB,KAAK;AACvD,SAAK,UAAU,QAAQ,aAAW,QAAQ,eAAe,IAAI,CAAC;AAC9D,SAAK,cAAc,MAAM;AACzB,SAAK,eAAe;AACpB,SAAK,4BAA4B,YAAY;AAC7C,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA,EAEA,uBAAuB,MAAM;AAC3B,SAAK,eAAe;AACpB,aAAS,GAAG,uBAAuB,EAAE,KAAK,UAAU,KAAK,iBAAiB,CAAC,EAAE,UAAU,MAAM;AAC3F,YAAM,OAAO,KAAK;AAClB,YAAM,aAAa,KAAK;AACxB,UAAI,KAAK,6BAA6B,4BAA4B,IAAI;AACpE,aAAK,SAAS,GAAG,CAAC,UAAU;AAAA,MAC9B,WAAW,KAAK,6BAA6B,4BAA4B,MAAM;AAC7E,aAAK,SAAS,GAAG,UAAU;AAAA,MAC7B;AACA,UAAI,KAAK,+BAA+B,8BAA8B,MAAM;AAC1E,aAAK,SAAS,CAAC,YAAY,CAAC;AAAA,MAC9B,WAAW,KAAK,+BAA+B,8BAA8B,OAAO;AAClF,aAAK,SAAS,YAAY,CAAC;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,GAAG,GAAG;AACrB,WAAO,KAAK,YAAY,QAAQ,mBAAmB,KAAK,UAAU,GAAG,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iCAAiC,MAAM,GAAG,GAAG;AAC3C,WAAO,KAAK,UAAU,KAAK,aAAW,QAAQ,YAAY,MAAM,GAAG,CAAC,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,GAAG,GAAG;AACtB,QAAI,CAAC,KAAK,YAAY,CAAC,mBAAmB,KAAK,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,eAAe,MAAM,IAAI,GAAG;AAClG,aAAO;AAAA,IACT;AACA,UAAM,mBAAmB,KAAK,eAAe,EAAE,iBAAiB,GAAG,CAAC;AAGpE,QAAI,CAAC,kBAAkB;AACrB,aAAO;AAAA,IACT;AAOA,WAAO,qBAAqB,KAAK,cAAc,KAAK,WAAW,SAAS,gBAAgB;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS,OAAO;AAC9B,UAAM,iBAAiB,KAAK;AAC5B,QAAI,CAAC,eAAe,IAAI,OAAO,KAAK,MAAM,MAAM,UAAQ;AAKtD,aAAO,KAAK,eAAe,MAAM,IAAI,KAAK,KAAK,YAAY,QAAQ,IAAI,IAAI;AAAA,IAC7E,CAAC,GAAG;AACF,qBAAe,IAAI,OAAO;AAC1B,WAAK,sBAAsB;AAC3B,WAAK,sBAAsB;AAC3B,WAAK,iBAAiB,KAAK;AAAA,QACzB,WAAW;AAAA,QACX,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,SAAK,gBAAgB,OAAO,OAAO;AACnC,SAAK,4BAA4B,YAAY;AAC7C,SAAK,iBAAiB,KAAK;AAAA,MACzB,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,SAAK,8BAA8B,KAAK,kBAAkB,SAAS,KAAK,eAAe,CAAC,EAAE,UAAU,WAAS;AAC3G,UAAI,KAAK,WAAW,GAAG;AACrB,cAAM,mBAAmB,KAAK,iBAAiB,aAAa,KAAK;AACjE,YAAI,kBAAkB;AACpB,eAAK,cAAc,eAAe,iBAAiB,KAAK,iBAAiB,IAAI;AAAA,QAC/E;AAAA,MACF,WAAW,KAAK,YAAY,GAAG;AAC7B,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB;AACf,QAAI,CAAC,KAAK,mBAAmB;AAC3B,YAAM,aAAa,eAAe,KAAK,UAAU;AACjD,WAAK,oBAAoB,cAAc,KAAK;AAAA,IAC9C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,2BAA2B;AACzB,UAAM,eAAe,KAAK,cAAc,uBAAuB,EAAE,OAAO,UAAQ,KAAK,WAAW,CAAC;AACjG,SAAK,UAAU,QAAQ,aAAW,QAAQ,gBAAgB,MAAM,YAAY,CAAC;AAAA,EAC/E;AACF;AAMA,SAAS,2BAA2B,YAAY,UAAU;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,SAAS;AAC5B,MAAI,YAAY,MAAM,cAAc,YAAY,MAAM,YAAY;AAChE,WAAO,4BAA4B;AAAA,EACrC,WAAW,YAAY,SAAS,cAAc,YAAY,SAAS,YAAY;AAC7E,WAAO,4BAA4B;AAAA,EACrC;AACA,SAAO,4BAA4B;AACrC;AAMA,SAAS,6BAA6B,YAAY,UAAU;AAC1D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,QAAQ;AAC3B,MAAI,YAAY,OAAO,cAAc,YAAY,OAAO,YAAY;AAClE,WAAO,8BAA8B;AAAA,EACvC,WAAW,YAAY,QAAQ,cAAc,YAAY,QAAQ,YAAY;AAC3E,WAAO,8BAA8B;AAAA,EACvC;AACA,SAAO,8BAA8B;AACvC;AAUA,SAAS,2BAA2B,SAAS,YAAY,WAAW,UAAU,UAAU;AACtF,QAAM,mBAAmB,2BAA2B,YAAY,QAAQ;AACxE,QAAM,qBAAqB,6BAA6B,YAAY,QAAQ;AAC5E,MAAI,0BAA0B,4BAA4B;AAC1D,MAAI,4BAA4B,8BAA8B;AAK9D,MAAI,kBAAkB;AACpB,UAAM,YAAY,QAAQ;AAC1B,QAAI,qBAAqB,4BAA4B,IAAI;AACvD,UAAI,YAAY,GAAG;AACjB,kCAA0B,4BAA4B;AAAA,MACxD;AAAA,IACF,WAAW,QAAQ,eAAe,YAAY,QAAQ,cAAc;AAClE,gCAA0B,4BAA4B;AAAA,IACxD;AAAA,EACF;AACA,MAAI,oBAAoB;AACtB,UAAM,aAAa,QAAQ;AAC3B,QAAI,cAAc,OAAO;AACvB,UAAI,uBAAuB,8BAA8B,OAAO;AAE9D,YAAI,aAAa,GAAG;AAClB,sCAA4B,8BAA8B;AAAA,QAC5D;AAAA,MACF,WAAW,QAAQ,cAAc,aAAa,QAAQ,aAAa;AACjE,oCAA4B,8BAA8B;AAAA,MAC5D;AAAA,IACF,OAAO;AACL,UAAI,uBAAuB,8BAA8B,MAAM;AAC7D,YAAI,aAAa,GAAG;AAClB,sCAA4B,8BAA8B;AAAA,QAC5D;AAAA,MACF,WAAW,QAAQ,cAAc,aAAa,QAAQ,aAAa;AACjE,oCAA4B,8BAA8B;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,yBAAyB,yBAAyB;AAC5D;AAGA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AACX;AAEA,IAAM,8BAA8B;AAAA,EAClC,SAAS;AAAA,EACT,SAAS;AACX;AAKA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,6BAA6B,EAAE;AAAA,IAC3C,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,uBAAuB,IAAI,KAAK;AAAA,IAAC;AAAA,IACpD,QAAQ,CAAC,mLAAmL;AAAA,IAC5L,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,6BAA6B;AAAA,MAC/B;AAAA,MACA,QAAQ,CAAC,mLAAmL;AAAA,IAC9L,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,eAAe,OAAO,sBAAsB;AAAA,EAC5C,YAAY,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAAA,EAC9D;AAAA;AAAA,EAEA,iBAAiB,oBAAI,IAAI;AAAA;AAAA,EAEzB,iBAAiB,oBAAI,IAAI;AAAA;AAAA,EAEzB,uBAAuB,OAAO,CAAC,CAAC;AAAA;AAAA,EAEhC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,UAAQ,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7C,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,cAAc,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,YAAY,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,SAAS,IAAI,QAAQ;AAAA,EACrB,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,sBAAsB,MAAM;AAC1B,QAAI,CAAC,KAAK,eAAe,IAAI,IAAI,GAAG;AAClC,WAAK,eAAe,IAAI,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,MAAM;AACrB,SAAK,eAAe,IAAI,IAAI;AAI5B,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,WAAK,QAAQ,kBAAkB,MAAM;AAGnC,aAAK,4BAA4B;AACjC,aAAK,4BAA4B,sBAAsB,KAAK,WAAW,KAAK,WAAW,aAAa,KAAK,8BAA8B,2BAA2B;AAAA,MACpK,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,MAAM;AACxB,SAAK,eAAe,OAAO,IAAI;AAAA,EACjC;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,SAAK,eAAe,OAAO,IAAI;AAC/B,SAAK,aAAa,IAAI;AACtB,QAAI,KAAK,eAAe,SAAS,GAAG;AAClC,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,MAAM,OAAO;AAEzB,QAAI,KAAK,qBAAqB,EAAE,QAAQ,IAAI,IAAI,IAAI;AAClD;AAAA,IACF;AACA,SAAK,aAAa,KAAK,aAAa;AACpC,SAAK,qBAAqB,OAAO,eAAa,CAAC,GAAG,WAAW,IAAI,CAAC;AAClE,QAAI,KAAK,qBAAqB,EAAE,WAAW,GAAG;AAI5C,YAAMC,gBAAe,MAAM,KAAK,WAAW,OAAO;AAClD,YAAM,kBAAkB,OAAK,KAAK,UAAU,KAAK,CAAC;AAClD,YAAM,SAAS;AAAA;AAAA;AAAA,QAGf,CAAC,UAAU,OAAK,KAAK,OAAO,KAAK,CAAC,GAAG,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA,QAK1D,CAAC,eAAe,KAAK,8BAA8B,2BAA2B;AAAA,MAAC;AAC/E,UAAIA,eAAc;AAChB,eAAO,KAAK,CAAC,YAAY,iBAAiB,qBAAqB,GAAG,CAAC,eAAe,iBAAiB,qBAAqB,CAAC;AAAA,MAC3H,OAAO;AACL,eAAO,KAAK,CAAC,WAAW,iBAAiB,qBAAqB,CAAC;AAAA,MACjE;AAGA,UAAI,CAACA,eAAc;AACjB,eAAO,KAAK,CAAC,aAAa,OAAK,KAAK,YAAY,KAAK,CAAC,GAAG,2BAA2B,CAAC;AAAA,MACvF;AACA,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,mBAAmB,OAAO,IAAI,CAAC,CAAC,MAAM,SAAS,OAAO,MAAM,sBAAsB,KAAK,WAAW,KAAK,WAAW,MAAM,SAAS,OAAO,CAAC;AAAA,MAChJ,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,MAAM;AACjB,SAAK,qBAAqB,OAAO,eAAa;AAC5C,YAAM,QAAQ,UAAU,QAAQ,IAAI;AACpC,UAAI,QAAQ,IAAI;AACd,kBAAU,OAAO,OAAO,CAAC;AACzB,eAAO,CAAC,GAAG,SAAS;AAAA,MACtB;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,KAAK,qBAAqB,EAAE,WAAW,GAAG;AAC5C,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,WAAO,KAAK,qBAAqB,EAAE,QAAQ,IAAI,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,YAAY;AACnB,UAAM,UAAU,CAAC,KAAK,MAAM;AAC5B,QAAI,cAAc,eAAe,KAAK,WAAW;AAI/C,cAAQ,KAAK,IAAI,WAAW,cAAY;AACtC,eAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,gBAAM,UAAU,sBAAsB,KAAK,WAAW,YAAY,UAAU,WAAS;AACnF,gBAAI,KAAK,qBAAqB,EAAE,QAAQ;AACtC,uBAAS,KAAK,KAAK;AAAA,YACrB;AAAA,UACF,GAAG,qBAAqB;AACxB,iBAAO,MAAM;AACX,oBAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,MAAM,GAAG,OAAO;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,MAAM,SAAS;AACnC,SAAK,0BAA0B,oBAAI,QAAQ;AAC3C,SAAK,sBAAsB,IAAI,MAAM,OAAO;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,MAAM;AACxB,SAAK,uBAAuB,OAAO,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,MAAM;AAC5B,WAAO,KAAK,uBAAuB,IAAI,IAAI,KAAK;AAAA,EAClD;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,QAAQ,cAAY,KAAK,eAAe,QAAQ,CAAC;AACrE,SAAK,eAAe,QAAQ,cAAY,KAAK,oBAAoB,QAAQ,CAAC;AAC1E,SAAK,wBAAwB;AAC7B,SAAK,sBAAsB;AAC3B,SAAK,YAAY,SAAS;AAC1B,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,+BAA+B,WAAS;AACtC,QAAI,KAAK,qBAAqB,EAAE,SAAS,GAAG;AAC1C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,+BAA+B,WAAS;AACtC,QAAI,KAAK,qBAAqB,EAAE,SAAS,GAAG;AAI1C,UAAI,KAAK,qBAAqB,EAAE,KAAK,KAAK,kBAAkB,GAAG;AAC7D,cAAM,eAAe;AAAA,MACvB;AACA,WAAK,YAAY,KAAK,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,kBAAkB,QAAQ,aAAW,QAAQ,CAAC;AACnD,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,iBAAiB;AAAA,EACrB,oBAAoB;AAAA,EACpB,iCAAiC;AACnC;AAIA,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,iBAAiB,OAAO,aAAa;AAAA,EACrC,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,YAAY,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAAA,EAC9D,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMf,WAAW,SAAS,SAAS,gBAAgB;AAC3C,WAAO,IAAI,QAAQ,SAAS,QAAQ,KAAK,WAAW,KAAK,SAAS,KAAK,gBAAgB,KAAK,mBAAmB,KAAK,SAAS;AAAA,EAC/H;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,WAAO,IAAI,YAAY,SAAS,KAAK,mBAAmB,KAAK,WAAW,KAAK,SAAS,KAAK,cAAc;AAAA,EAC3G;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,UAAS;AAAA,IAClB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAQH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAO5D,SAAS,kBAAkB,MAAM,MAAM;AACrC,MAAI,KAAK,aAAa,GAAG;AACvB,UAAM,MAAM,GAAG,IAAI,gEAAqE,KAAK,QAAQ,IAAI;AAAA,EAC3G;AACF;AAOA,IAAM,kBAAkB,IAAI,eAAe,eAAe;AAE1D,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO,UAAU;AAAA,EAC3B,cAAc,OAAO,iBAAiB;AAAA,IACpC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB,OAAO,gBAAgB;AAAA;AAAA,EAE3C,gBAAgB,IAAI,QAAQ;AAAA;AAAA,EAE5B,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,cAAc,KAAK,IAAI;AAAA,EAC9B;AAAA,EACA,YAAY;AAAA,EACZ,cAAc;AACZ,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAkB,KAAK,QAAQ,eAAe,eAAe;AAAA,IAC/D;AACA,SAAK,aAAa,WAAW,IAAI;AAAA,EACnC;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,aAAa;AACrB,UAAI,SAAS,KAAK,QAAQ,cAAc;AACxC,aAAO,QAAQ;AACb,cAAM,MAAM,KAAK,kBAAkB,wBAAwB,MAAM;AACjE,YAAI,KAAK;AACP,eAAK,cAAc;AACnB,cAAI,WAAW,IAAI;AACnB;AAAA,QACF;AACA,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,cAAc,IAAI;AACpC,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,WAAW,CAAC,GAAG,iBAAiB;AAAA,IAChC,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,yBAAyB,YAAY,gBAAgB;AAAA,IACrE;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAO5D,IAAM,gBAAgB,IAAI,eAAe,aAAa;AAEtD,IAAM,UAAN,MAAM,SAAQ;AAAA,EACZ,UAAU,OAAO,UAAU;AAAA,EAC3B,gBAAgB,OAAO,eAAe;AAAA,IACpC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,OAAO,MAAM;AAAA,EACvB,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,iBAAiB;AAAA,IACpC,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,cAAc,OAAO,iBAAiB;AAAA,IACpC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,aAAa,IAAI,QAAQ;AAAA,EACzB,WAAW,IAAI,gBAAgB,CAAC,CAAC;AAAA,EACjC;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,CAAC,EAAE,KAAK,iBAAiB,KAAK,cAAc;AAAA,EACvE;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,SAAS,WAAW,KAAK;AAAA,EAChC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAAA;AAAA,EAER,UAAU,IAAI,aAAa;AAAA;AAAA,EAE3B,WAAW,IAAI,aAAa;AAAA;AAAA,EAE5B,QAAQ,IAAI,aAAa;AAAA;AAAA,EAEzB,UAAU,IAAI,aAAa;AAAA;AAAA,EAE3B,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,UAAU,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,QAAQ,IAAI,WAAW,cAAY;AACjC,UAAM,eAAe,KAAK,SAAS,MAAM,KAAK,IAAI,iBAAe;AAAA,MAC/D,QAAQ;AAAA,MACR,iBAAiB,WAAW;AAAA,MAC5B,OAAO,WAAW;AAAA,MAClB,OAAO,WAAW;AAAA,MAClB,UAAU,WAAW;AAAA,IACvB,EAAE,CAAC,EAAE,UAAU,QAAQ;AACvB,WAAO,MAAM;AACX,mBAAa,YAAY;AAAA,IAC3B;AAAA,EACF,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,gBAAgB,KAAK;AAC3B,UAAM,SAAS,OAAO,iBAAiB;AAAA,MACrC,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,QAAQ;AAChC,SAAK,WAAW,SAAS,WAAW,KAAK,SAAS;AAAA,MAChD,oBAAoB,UAAU,OAAO,sBAAsB,OAAO,OAAO,qBAAqB;AAAA,MAC9F,iCAAiC,UAAU,OAAO,mCAAmC,OAAO,OAAO,kCAAkC;AAAA,MACrI,QAAQ,QAAQ;AAAA,IAClB,CAAC;AACD,SAAK,SAAS,OAAO;AACrB,SAAK,kBAAkB,sBAAsB,KAAK,QAAQ,eAAe,IAAI;AAC7E,QAAI,QAAQ;AACV,WAAK,gBAAgB,MAAM;AAAA,IAC7B;AAQA,QAAI,eAAe;AACjB,WAAK,SAAS,mBAAmB,cAAc,YAAY;AAC3D,oBAAc,QAAQ,IAAI;AAE1B,oBAAc,aAAa,cAAc,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AACxF,aAAK,SAAS,QAAQ,KAAK;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,SAAK,YAAY,KAAK,QAAQ;AAC9B,SAAK,cAAc,KAAK,QAAQ;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB;AACtB,WAAO,KAAK,SAAS,sBAAsB;AAAA,EAC7C;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,SAAS,eAAe;AAAA,EACtC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AACpB,WAAO,KAAK,SAAS,oBAAoB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,OAAO;AACzB,SAAK,SAAS,oBAAoB,KAAK;AAAA,EACzC;AAAA,EACA,kBAAkB;AAKhB,oBAAgB,MAAM;AACpB,WAAK,mBAAmB;AACxB,WAAK,sBAAsB;AAC3B,WAAK,SAAS,QAAQ,KAAK;AAC3B,UAAI,KAAK,kBAAkB;AACzB,aAAK,SAAS,oBAAoB,KAAK,gBAAgB;AAAA,MACzD;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,qBAAqB,QAAQ,qBAAqB;AACxD,UAAM,iBAAiB,QAAQ,kBAAkB;AAGjD,QAAI,sBAAsB,CAAC,mBAAmB,aAAa;AACzD,WAAK,mBAAmB;AAAA,IAC1B;AAEA,SAAK,SAAS,QAAQ,KAAK;AAG3B,QAAI,kBAAkB,CAAC,eAAe,eAAe,KAAK,kBAAkB;AAC1E,WAAK,SAAS,oBAAoB,KAAK,gBAAgB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,WAAW,IAAI;AAAA,IACpC;AACA,SAAK,kBAAkB,oBAAoB,KAAK,QAAQ,aAAa;AAErE,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,SAAS,SAAS;AACvB,WAAK,WAAW,KAAK;AACrB,WAAK,WAAW,SAAS;AACzB,WAAK,SAAS,QAAQ;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,WAAW,QAAQ;AACjB,UAAM,UAAU,KAAK,SAAS,SAAS;AACvC,YAAQ,KAAK,MAAM;AACnB,SAAK,SAAS,KAAK,OAAO;AAAA,EAC5B;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,UAAU,KAAK,SAAS,SAAS;AACvC,UAAM,QAAQ,QAAQ,QAAQ,MAAM;AACpC,QAAI,QAAQ,IAAI;AACd,cAAQ,OAAO,OAAO,CAAC;AACvB,WAAK,SAAS,KAAK,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS;AAC3B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,sBAAsB,SAAS;AAC7B,QAAI,YAAY,KAAK,kBAAkB;AACrC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,wBAAwB,aAAa;AACnC,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,0BAA0B,aAAa;AACrC,QAAI,gBAAgB,KAAK,sBAAsB;AAC7C,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AACnB,UAAM,UAAU,KAAK,QAAQ;AAC7B,QAAI,cAAc;AAClB,QAAI,KAAK,qBAAqB;AAC5B,oBAAc,QAAQ,YAAY,SAAY,QAAQ,QAAQ,KAAK,mBAAmB;AAAA;AAAA,QAEtF,QAAQ,eAAe,QAAQ,KAAK,mBAAmB;AAAA;AAAA,IACzD;AACA,QAAI,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAClE,wBAAkB,aAAa,SAAS;AAAA,IAC1C;AACA,SAAK,SAAS,gBAAgB,eAAe,OAAO;AAAA,EACtD;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,WAAW,KAAK;AACtB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,OAAO,aAAa,UAAU;AAChC,aAAO,KAAK,QAAQ,cAAc,QAAQ,QAAQ;AAAA,IACpD;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AAAA;AAAA,EAEA,YAAY,KAAK;AACf,QAAI,cAAc,UAAU,MAAM;AAChC,UAAI,CAAC,IAAI,WAAW,GAAG;AACrB,cAAM,MAAM,KAAK;AACjB,cAAM,iBAAiB,KAAK;AAC5B,cAAM,cAAc,KAAK,uBAAuB;AAAA,UAC9C,UAAU,KAAK,qBAAqB;AAAA,UACpC,SAAS,KAAK,qBAAqB;AAAA,UACnC,eAAe,KAAK;AAAA,QACtB,IAAI;AACJ,cAAM,UAAU,KAAK,mBAAmB;AAAA,UACtC,UAAU,KAAK,iBAAiB;AAAA,UAChC,SAAS,KAAK,iBAAiB;AAAA,UAC/B,WAAW,KAAK,iBAAiB;AAAA,UACjC,eAAe,KAAK;AAAA,QACtB,IAAI;AACJ,YAAI,WAAW,KAAK;AACpB,YAAI,WAAW,KAAK;AACpB,YAAI,QAAQ,KAAK;AACjB,YAAI,iBAAiB,OAAO,mBAAmB,YAAY,iBAAiB,iBAAiB,qBAAqB,cAAc;AAChI,YAAI,oBAAoB,KAAK;AAC7B,YAAI,eAAe,KAAK;AACxB,YAAI,oBAAoB,KAAK,oBAAoB,CAAC,EAAE,wBAAwB,WAAW,EAAE,oBAAoB,OAAO,EAAE,qBAAqB,KAAK,oBAAoB,QAAQ;AAC5K,YAAI,KAAK;AACP,cAAI,cAAc,IAAI,KAAK;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,cAAc,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAE9C,UAAI,KAAK,aAAa;AACpB,YAAI,WAAW,KAAK,YAAY,QAAQ;AACxC;AAAA,MACF;AAGA,UAAI,SAAS,KAAK,QAAQ,cAAc;AACxC,aAAO,QAAQ;AACb,cAAM,aAAa,KAAK,kBAAkB,wBAAwB,MAAM;AACxE,YAAI,YAAY;AACd,cAAI,WAAW,WAAW,QAAQ;AAClC;AAAA,QACF;AACA,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc,KAAK;AACjB,QAAI,QAAQ,UAAU,gBAAc;AAClC,WAAK,QAAQ,KAAK;AAAA,QAChB,QAAQ;AAAA,QACR,OAAO,WAAW;AAAA,MACpB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,SAAS,UAAU,kBAAgB;AACrC,WAAK,SAAS,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,OAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,MAAM,UAAU,cAAY;AAC9B,WAAK,MAAM,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,UAAU,SAAS;AAAA,QACnB,WAAW,SAAS;AAAA,QACpB,OAAO,SAAS;AAAA,MAClB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,QAAQ,UAAU,gBAAc;AAClC,WAAK,QAAQ,KAAK;AAAA,QAChB,WAAW,WAAW,UAAU;AAAA,QAChC,MAAM;AAAA,QACN,cAAc,WAAW;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,UAAU,eAAa;AAChC,WAAK,OAAO,KAAK;AAAA,QACf,WAAW,UAAU,UAAU;AAAA,QAC/B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AACD,QAAI,QAAQ,UAAU,eAAa;AACjC,WAAK,QAAQ,KAAK;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,cAAc,UAAU;AAAA,QACxB,mBAAmB,UAAU,kBAAkB;AAAA,QAC/C,WAAW,UAAU,UAAU;AAAA,QAC/B,wBAAwB,UAAU;AAAA,QAClC,MAAM;AAAA,QACN,UAAU,UAAU;AAAA,QACpB,WAAW,UAAU;AAAA,QACrB,OAAO,UAAU;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,oBAAoB,OAAO,QAAQ;AACnD,SAAK,iBAAiB,kBAAkB;AACxC,QAAI,UAAU;AACZ,WAAK,WAAW;AAAA,IAClB;AACA,QAAI,mBAAmB;AACrB,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,cAAc;AAChB,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,iBAAiB;AACnB,WAAK,kBAAkB;AAAA,IACzB;AACA,QAAI,qBAAqB;AACvB,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,kBAAkB;AACpB,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AAEtB,SAAK,SAAS;AAAA;AAAA,MAEd,IAAI,aAAW;AACb,cAAM,iBAAiB,QAAQ,IAAI,YAAU,OAAO,OAAO;AAI3D,YAAI,KAAK,eAAe,KAAK,qBAAqB;AAChD,yBAAe,KAAK,KAAK,OAAO;AAAA,QAClC;AACA,aAAK,SAAS,YAAY,cAAc;AAAA,MAC1C,CAAC;AAAA;AAAA,MAED,UAAU,aAAW;AACnB,eAAO,MAAM,GAAG,QAAQ,IAAI,UAAQ,KAAK,cAAc,KAAK,UAAU,IAAI,CAAC,CAAC,CAAC;AAAA,MAC/E,CAAC;AAAA,MAAG,UAAU,KAAK,UAAU;AAAA,IAAC,EAAE,UAAU,oBAAkB;AAE1D,YAAM,UAAU,KAAK;AACrB,YAAM,SAAS,eAAe,QAAQ;AACtC,qBAAe,WAAW,QAAQ,cAAc,MAAM,IAAI,QAAQ,aAAa,MAAM;AAAA,IACvF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAS;AAAA,EAC5C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,IAC/B,WAAW,CAAC,GAAG,UAAU;AAAA,IACzB,UAAU;AAAA,IACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qBAAqB,IAAI,QAAQ,EAAE,qBAAqB,IAAI,SAAS,WAAW,CAAC;AAAA,MAClG;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,eAAe,MAAM;AAAA,MAC/B,UAAU,CAAC,GAAG,mBAAmB,UAAU;AAAA,MAC3C,qBAAqB,CAAC,GAAG,sBAAsB,qBAAqB;AAAA,MACpE,iBAAiB,CAAC,GAAG,mBAAmB,iBAAiB;AAAA,MACzD,gBAAgB,CAAC,GAAG,qBAAqB,gBAAgB;AAAA,MACzD,kBAAkB,CAAC,GAAG,2BAA2B,kBAAkB;AAAA,MACnE,UAAU,CAAC,GAAG,mBAAmB,YAAY,gBAAgB;AAAA,MAC7D,mBAAmB,CAAC,GAAG,4BAA4B,mBAAmB;AAAA,MACtE,cAAc,CAAC,GAAG,uBAAuB,cAAc;AAAA,MACvD,kBAAkB,CAAC,GAAG,2BAA2B,kBAAkB;AAAA,MACnE,OAAO,CAAC,GAAG,gBAAgB,SAAS,eAAe;AAAA,IACrD;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,MAC/B;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,sBAAsB,IAAI,eAAe,kBAAkB;AAOjE,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA,EAErB,SAAS,oBAAI,IAAI;AAAA;AAAA,EAEjB,WAAW;AAAA,EACX,cAAc;AACZ,SAAK,OAAO,MAAM;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,4BAA4B,YAAY,gBAAgB;AAAA,IACxE;AAAA,IACA,UAAU,CAAC,kBAAkB;AAAA,IAC7B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,UAAU,OAAO,UAAU;AAAA,EAC3B,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS,OAAO,qBAAqB;AAAA,IACnC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA,OAAO,aAAa,CAAC;AAAA;AAAA,EAErB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,CAAC;AAAA;AAAA,EAEf;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO,YAAY,EAAE,MAAM,gBAAgB;AAAA;AAAA,EAEhD;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,CAAC,CAAC,KAAK,UAAU,KAAK,OAAO;AAAA,EACxD;AAAA,EACA,IAAI,SAAS,OAAO;AAKlB,SAAK,aAAa,WAAW,KAAK,YAAY;AAAA,EAChD;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,MAAM;AAAA;AAAA,EAEvB,gBAAgB,MAAM;AAAA;AAAA,EAEtB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA;AAAA;AAAA,EAEA,UAAU,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAI3B,UAAU,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,iBAAiB,oBAAI,IAAI;AAAA,EACzB,cAAc;AACZ,UAAM,WAAW,OAAO,QAAQ;AAChC,UAAM,SAAS,OAAO,iBAAiB;AAAA,MACrC,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAkB,KAAK,QAAQ,eAAe,aAAa;AAAA,IAC7D;AACA,SAAK,eAAe,SAAS,eAAe,KAAK,OAAO;AACxD,SAAK,aAAa,OAAO;AACzB,QAAI,QAAQ;AACV,WAAK,gBAAgB,MAAM;AAAA,IAC7B;AACA,SAAK,aAAa,iBAAiB,CAAC,MAAM,SAAS;AACjD,aAAO,KAAK,eAAe,KAAK,MAAM,KAAK,IAAI;AAAA,IACjD;AACA,SAAK,aAAa,gBAAgB,CAAC,OAAO,MAAM,SAAS;AACvD,aAAO,KAAK,cAAc,OAAO,KAAK,MAAM,KAAK,IAAI;AAAA,IACvD;AACA,SAAK,4BAA4B,KAAK,YAAY;AAClD,SAAK,cAAc,KAAK,YAAY;AACpC,iBAAY,WAAW,KAAK,IAAI;AAChC,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,OAAO,IAAI,IAAI;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,SAAK,eAAe,IAAI,IAAI;AAG5B,QAAI,KAAK,aAAa,WAAW,GAAG;AAClC,WAAK,kBAAkB,KAAK,eAAe,EAAE,IAAI,CAAAH,UAAQA,MAAK,QAAQ,CAAC;AAAA,IACzE;AAAA,EACF;AAAA;AAAA,EAEA,WAAW,MAAM;AACf,SAAK,eAAe,OAAO,IAAI;AAI/B,QAAI,KAAK,mBAAmB;AAC1B,YAAM,QAAQ,KAAK,kBAAkB,QAAQ,KAAK,QAAQ;AAC1D,UAAI,QAAQ,IAAI;AACd,aAAK,kBAAkB,OAAO,OAAO,CAAC;AACtC,aAAK,kBAAkB,KAAK,iBAAiB;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,MAAM,KAAK,KAAK,cAAc,EAAE,KAAK,CAAC,GAAG,MAAM;AACpD,YAAM,mBAAmB,EAAE,SAAS,kBAAkB,EAAE,wBAAwB,EAAE,SAAS,kBAAkB,CAAC;AAI9G,aAAO,mBAAmB,KAAK,8BAA8B,KAAK;AAAA,IACpE,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,aAAY,WAAW,QAAQ,IAAI;AACjD,QAAI,QAAQ,IAAI;AACd,mBAAY,WAAW,OAAO,OAAO,CAAC;AAAA,IACxC;AACA,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,OAAO,OAAO,IAAI;AAAA,IAChC;AACA,SAAK,oBAAoB;AACzB,SAAK,eAAe,MAAM;AAC1B,SAAK,aAAa,QAAQ;AAC1B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA,EAEA,4BAA4B,KAAK;AAC/B,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,OAAO,KAAK,UAAU,KAAK,KAAK,KAAK,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS,IAAI,cAAc,KAAK,CAAC;AAAA,IAC3H;AACA,QAAI,cAAc,UAAU,MAAM;AAChC,YAAM,WAAW,YAAY,KAAK,WAAW,EAAE,IAAI,UAAQ;AACzD,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,wBAAwB,aAAY,WAAW,KAAK,UAAQ,KAAK,OAAO,IAAI;AAClF,cAAI,CAAC,0BAA0B,OAAO,cAAc,eAAe,YAAY;AAC7E,oBAAQ,KAAK,2DAA2D,IAAI,GAAG;AAAA,UACjF;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AACD,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,OAAO,QAAQ,UAAQ;AACjC,cAAI,SAAS,QAAQ,IAAI,MAAM,IAAI;AACjC,qBAAS,KAAK,IAAI;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,CAAC,KAAK,4BAA4B;AACpC,cAAM,oBAAoB,KAAK,kBAAkB,4BAA4B,KAAK,OAAO,EAAE,IAAI,gBAAc,WAAW,cAAc,EAAE,aAAa;AACrJ,aAAK,aAAa,sBAAsB,iBAAiB;AAGzD,aAAK,6BAA6B;AAAA,MACpC;AACA,UAAI,KAAK,0BAA0B;AACjC,cAAM,YAAY,KAAK,QAAQ,cAAc,cAAc,KAAK,wBAAwB;AACxF,YAAI,CAAC,cAAc,OAAO,cAAc,eAAe,YAAY;AACjE,gBAAM,IAAI,MAAM,0EAA0E,KAAK,wBAAwB,GAAG;AAAA,QAC5H;AACA,YAAI,qBAAqB,SAAS;AAAA,MACpC;AACA,UAAI,WAAW,KAAK;AACpB,UAAI,WAAW,KAAK;AACpB,UAAI,kBAAkB,KAAK;AAC3B,UAAI,qBAAqB,KAAK;AAC9B,UAAI,iBAAiB,qBAAqB,KAAK,gBAAgB,CAAC;AAChE,UAAI,YAAY,SAAS,OAAO,UAAQ,QAAQ,SAAS,IAAI,EAAE,IAAI,UAAQ,KAAK,YAAY,CAAC,EAAE,gBAAgB,KAAK,WAAW;AAAA,IACjI,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc,KAAK;AACjB,QAAI,cAAc,UAAU,MAAM;AAChC,WAAK,kBAAkB,KAAK,eAAe,EAAE,IAAI,UAAQ,KAAK,QAAQ,CAAC;AACvE,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,QAAQ,UAAU,WAAS;AAC7B,WAAK,QAAQ,KAAK;AAAA,QAChB,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,QACjB,cAAc,MAAM;AAAA,MACtB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,OAAO,UAAU,WAAS;AAC5B,WAAK,OAAO,KAAK;AAAA,QACf,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,MACnB,CAAC;AACD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,QAAI,OAAO,UAAU,WAAS;AAC5B,WAAK,OAAO,KAAK;AAAA,QACf,eAAe,MAAM;AAAA,QACrB,cAAc,MAAM;AAAA,QACpB,WAAW;AAAA,QACX,MAAM,MAAM,KAAK;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,QAAQ,UAAU,eAAa;AACjC,WAAK,QAAQ,KAAK;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,cAAc,UAAU;AAAA,QACxB,mBAAmB,UAAU,kBAAkB;AAAA,QAC/C,WAAW,UAAU,UAAU;AAAA,QAC/B,MAAM,UAAU,KAAK;AAAA,QACrB,wBAAwB,UAAU;AAAA,QAClC,UAAU,UAAU;AAAA,QACpB,WAAW,UAAU;AAAA,QACrB,OAAO,UAAU;AAAA,MACnB,CAAC;AAGD,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AACD,UAAM,IAAI,kBAAkB,IAAI,gBAAgB,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAAA,EAC1G;AAAA;AAAA,EAEA,gBAAgB,QAAQ;AACtB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,WAAW,oBAAoB,OAAO,QAAQ;AACnD,SAAK,kBAAkB,mBAAmB,OAAO,QAAQ;AACzD,SAAK,qBAAqB,0BAA0B,OAAO,QAAQ;AACnE,SAAK,cAAc,mBAAmB;AACtC,QAAI,UAAU;AACZ,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,OAAO;AACvB,SAAK,oBAAoB;AACzB,SAAK,aAAa,UAAU,KAAK;AAAA,EACnC;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,GAAG,CAAC,eAAe,CAAC;AAAA,IACtD,WAAW,CAAC,GAAG,eAAe;AAAA,IAC9B,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,IAAI,EAAE;AAC3B,QAAG,YAAY,0BAA0B,IAAI,QAAQ,EAAE,0BAA0B,IAAI,aAAa,WAAW,CAAC,EAAE,2BAA2B,IAAI,aAAa,YAAY,CAAC;AAAA,MAC3K;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa,CAAC,GAAG,0BAA0B,aAAa;AAAA,MACxD,MAAM,CAAC,GAAG,mBAAmB,MAAM;AAAA,MACnC,aAAa,CAAC,GAAG,0BAA0B,aAAa;AAAA,MACxD,IAAI;AAAA,MACJ,UAAU,CAAC,GAAG,uBAAuB,UAAU;AAAA,MAC/C,UAAU,CAAC,GAAG,uBAAuB,YAAY,gBAAgB;AAAA,MACjE,iBAAiB,CAAC,GAAG,8BAA8B,mBAAmB,gBAAgB;AAAA,MACtF,gBAAgB,CAAC,GAAG,6BAA6B,gBAAgB;AAAA,MACjE,eAAe,CAAC,GAAG,4BAA4B,eAAe;AAAA,MAC9D,oBAAoB,CAAC,GAAG,iCAAiC,sBAAsB,gBAAgB;AAAA,MAC/F,gBAAgB,CAAC,GAAG,6BAA6B,gBAAgB;AAAA,MACjE,0BAA0B,CAAC,GAAG,+BAA+B,0BAA0B;AAAA,IACzF;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,mBAAmB;AAAA;AAAA,MAEjC;AAAA,QACE,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,MAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,IAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA;AAAA,QAEX;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,aAAa;AAAA,QACb,kCAAkC;AAAA,QAClC,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAmB,IAAI,eAAe,gBAAgB;AAK5D,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc,OAAO,WAAW;AAAA,EAChC,QAAQ,OAAO,iBAAiB;AAAA,IAC9B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA;AAAA,EAEA,YAAY;AAAA,EACZ,cAAc;AACZ,SAAK,OAAO,oBAAoB,IAAI;AAAA,EACtC;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,sBAAsB,IAAI;AAAA,EACxC;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,kBAAkB,EAAE,CAAC;AAAA,IACjD,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,IAC3D;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,uBAAuB,IAAI,eAAe,oBAAoB;AAKpE,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc,OAAO,WAAW;AAAA,EAChC,QAAQ,OAAO,iBAAiB;AAAA,IAC9B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,wBAAwB,IAAI;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,0BAA0B,IAAI;AAAA,EAC5C;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,sBAAsB,EAAE,CAAC;AAAA,IACrD,QAAQ;AAAA,MACN,MAAM;AAAA,IACR;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAuB,CAAC,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AACvH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AAAA,IACnG,SAAS,CAAC,qBAAqB,aAAa,kBAAkB,SAAS,eAAe,gBAAgB,kBAAkB;AAAA,EAC1H,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,QAAQ;AAAA,IACpB,SAAS,CAAC,mBAAmB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS;AAAA,MACT,SAAS,CAAC,qBAAqB,GAAG,oBAAoB;AAAA,MACtD,WAAW,CAAC,QAAQ;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["importantProperties", "item", "AutoScrollVerticalDirection", "AutoScrollHorizontalDirection", "isTouchEvent"]}