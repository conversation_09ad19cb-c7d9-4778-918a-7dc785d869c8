from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import User, Company, Project, Post
from django.db import transaction

class Command(BaseCommand):
    help = 'Clear and reinitialize database with proper role-based test data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to delete all data',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This will delete ALL data in the database. '
                    'Use --confirm to proceed.'
                )
            )
            return

        self.stdout.write('Starting database reset...')
        
        with transaction.atomic():
            # Clear all existing data
            self.stdout.write('Clearing existing data...')
            Post.objects.all().delete()
            Project.objects.all().delete()
            Company.objects.all().delete()
            User.objects.all().delete()
            
            # Create Super Admin
            self.stdout.write('Creating Super Admin...')
            super_admin = User.objects.create_user(
                username='superadmin',
                email='<EMAIL>',
                password='admin123',
                first_name='Super',
                last_name='Admin',
                role='super_admin',
                is_staff=True,
                is_superuser=True
            )
            
            # Create Companies
            self.stdout.write('Creating companies...')
            companies_data = [
                {'name': 'TechCorp Solutions', 'is_active': True},
                {'name': 'Digital Marketing Pro', 'is_active': True},
                {'name': 'Creative Agency Ltd', 'is_active': True},
            ]
            
            companies = []
            for company_data in companies_data:
                company = Company.objects.create(**company_data)
                companies.append(company)
                self.stdout.write(f'  Created company: {company.name}')
            
            # Create Company Admins
            self.stdout.write('Creating Company Admins...')
            company_admins_data = [
                {
                    'username': 'admin_techcorp',
                    'email': '<EMAIL>',
                    'password': 'admin123',
                    'first_name': 'John',
                    'last_name': 'Smith',
                    'role': 'company_admin',
                    'company': companies[0]  # TechCorp Solutions
                },
                {
                    'username': 'admin_digital',
                    'email': '<EMAIL>',
                    'password': 'admin123',
                    'first_name': 'Sarah',
                    'last_name': 'Johnson',
                    'role': 'company_admin',
                    'company': companies[1]  # Digital Marketing Pro
                },
                {
                    'username': 'admin_creative',
                    'email': '<EMAIL>',
                    'password': 'admin123',
                    'first_name': 'Mike',
                    'last_name': 'Wilson',
                    'role': 'company_admin',
                    'company': companies[2]  # Creative Agency Ltd
                }
            ]
            
            company_admins = []
            for admin_data in company_admins_data:
                admin = User.objects.create_user(**admin_data)
                company_admins.append(admin)
                self.stdout.write(f'  Created admin: {admin.username} for {admin.company.name}')
            
            # Create Creators
            self.stdout.write('Creating Creators...')
            creators_data = [
                # TechCorp Creators
                {
                    'username': 'creator_alice',
                    'email': '<EMAIL>',
                    'password': 'creator123',
                    'first_name': 'Alice',
                    'last_name': 'Brown',
                    'role': 'creator',
                    'company': companies[0]
                },
                {
                    'username': 'creator_bob',
                    'email': '<EMAIL>',
                    'password': 'creator123',
                    'first_name': 'Bob',
                    'last_name': 'Davis',
                    'role': 'creator',
                    'company': companies[0]
                },
                # Digital Marketing Creators
                {
                    'username': 'creator_carol',
                    'email': '<EMAIL>',
                    'password': 'creator123',
                    'first_name': 'Carol',
                    'last_name': 'Miller',
                    'role': 'creator',
                    'company': companies[1]
                },
                {
                    'username': 'creator_david',
                    'email': '<EMAIL>',
                    'password': 'creator123',
                    'first_name': 'David',
                    'last_name': 'Garcia',
                    'role': 'creator',
                    'company': companies[1]
                },
                # Creative Agency Creators
                {
                    'username': 'creator_eve',
                    'email': '<EMAIL>',
                    'password': 'creator123',
                    'first_name': 'Eve',
                    'last_name': 'Martinez',
                    'role': 'creator',
                    'company': companies[2]
                }
            ]
            
            creators = []
            for creator_data in creators_data:
                creator = User.objects.create_user(**creator_data)
                creators.append(creator)
                self.stdout.write(f'  Created creator: {creator.username} for {creator.company.name}')
            
            # Create Projects
            self.stdout.write('Creating Projects...')
            from datetime import datetime, timedelta
            
            projects_data = [
                # TechCorp Projects
                {
                    'name': 'Q1_Social_Campaign',
                    'title': 'Q1 Social Media Campaign',
                    'description': 'Social media content for Q1 product launch',
                    'deadline': datetime.now() + timedelta(days=30),
                    'company': companies[0],
                    'created_by': company_admins[0],
                    'creators': [creators[0], creators[1]]  # Alice and Bob
                },
                {
                    'name': 'Tech_Blog_Series',
                    'title': 'Technical Blog Series',
                    'description': 'Weekly technical blog posts',
                    'deadline': datetime.now() + timedelta(days=45),
                    'company': companies[0],
                    'created_by': company_admins[0],
                    'creators': [creators[0]]  # Alice only
                },
                # Digital Marketing Projects
                {
                    'name': 'Client_Content_Pack',
                    'title': 'Client Content Package',
                    'description': 'Monthly content package for key client',
                    'deadline': datetime.now() + timedelta(days=20),
                    'company': companies[1],
                    'created_by': company_admins[1],
                    'creators': [creators[2], creators[3]]  # Carol and David
                },
                # Creative Agency Projects
                {
                    'name': 'Brand_Refresh',
                    'title': 'Brand Refresh Campaign',
                    'description': 'Complete brand refresh for major client',
                    'deadline': datetime.now() + timedelta(days=60),
                    'company': companies[2],
                    'created_by': company_admins[2],
                    'creators': [creators[4]]  # Eve only
                }
            ]
            
            for project_data in projects_data:
                project_creators = project_data.pop('creators')
                project = Project.objects.create(**project_data)
                project.creators.set(project_creators)
                self.stdout.write(f'  Created project: {project.title} for {project.company.name}')
                self.stdout.write(f'    Assigned creators: {[c.username for c in project_creators]}')
        
        self.stdout.write(
            self.style.SUCCESS(
                '\nDatabase reset completed successfully!\n'
                '\nTest accounts created:\n'
                '- Super Admin: superadmin / admin123\n'
                '- Company Admins: admin_techcorp, admin_digital, admin_creative / admin123\n'
                '- Creators: creator_alice, creator_bob, creator_carol, creator_david, creator_eve / creator123\n'
                '\nEach role has proper company assignments and project relationships.'
            )
        )
