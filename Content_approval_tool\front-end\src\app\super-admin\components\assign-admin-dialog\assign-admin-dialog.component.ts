import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SuperAdminService } from '../../services/super-admin.service';
import { Company, User, AssignmentRequest } from '../../models/super-admin.models';

@Component({
  selector: 'app-assign-admin-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <h2 mat-dialog-title>
      <mat-icon>admin_panel_settings</mat-icon>
      Assign Company Admin
    </h2>

    <mat-dialog-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading data...</p>
      </div>

      <form [formGroup]="assignmentForm" class="assignment-form" *ngIf="!isLoading">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Company Admin</mat-label>
          <mat-select formControlName="user_id" placeholder="Choose a company admin">
            <mat-option *ngFor="let user of availableUsers" [value]="user.id">
              {{ user.username }} ({{ user.role }})
              <span *ngIf="user.company"> - Currently at {{ user.company.name }}</span>
            </mat-option>
          </mat-select>
          <mat-hint>Only showing users with Company Admin role</mat-hint>
          <mat-error *ngIf="assignmentForm.get('user_id')?.hasError('required')">
            Please select a company admin
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Company</mat-label>
          <mat-select formControlName="company_id" placeholder="Choose a company">
            <mat-option *ngFor="let company of companies" [value]="company.id">
              {{ company.name }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="assignmentForm.get('company_id')?.hasError('required')">
            Please select a company
          </mat-error>
        </mat-form-field>
      </form>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button 
              color="primary" 
              (click)="onAssign()"
              [disabled]="assignmentForm.invalid || isLoading">
        <mat-icon>person_add</mat-icon>
        Assign Admin
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .assignment-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-width: 400px;
    }

    .full-width {
      width: 100%;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      text-align: center;
    }

    .loading-container p {
      margin-top: 16px;
      color: #666;
    }

    mat-dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    mat-dialog-content {
      padding: 20px 24px;
    }

    mat-dialog-actions {
      padding: 16px 24px;
    }
  `]
})
export class AssignAdminDialogComponent implements OnInit {
  assignmentForm: FormGroup;
  companies: Company[] = [];
  availableUsers: User[] = [];
  isLoading = true;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AssignAdminDialogComponent>,
    private superAdminService: SuperAdminService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.assignmentForm = this.fb.group({
      user_id: ['', Validators.required],
      company_id: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.isLoading = true;

    // Load companies and users with company_admin role only
    Promise.all([
      this.superAdminService.getCompanies().toPromise(),
      this.superAdminService.getCompanyAdmins().toPromise()
    ]).then(([companies, users]) => {
      this.companies = companies || [];
      this.availableUsers = users || [];
      this.isLoading = false;
    }).catch(error => {
      console.error('Error loading data:', error);
      this.isLoading = false;
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAssign(): void {
    if (this.assignmentForm.valid) {
      const assignment: AssignmentRequest = this.assignmentForm.value;
      this.dialogRef.close(assignment);
    }
  }
}
