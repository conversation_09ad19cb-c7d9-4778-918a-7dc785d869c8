<div class="blog-preview-container" *ngIf="blog">
  <!-- Cover Image -->
  <div class="cover-image-section" *ngIf="coverImageUrl || blog.cover_image">
    <img 
      [src]="coverImageUrl || blog.cover_image" 
      [alt]="blog.title"
      class="cover-image">
  </div>

  <!-- Blog Header -->
  <div class="blog-header">
    <h1 class="blog-title">{{ blog.title }}</h1>
    
    <!-- Metadata -->
    <div class="blog-metadata" *ngIf="showMetadata">
      <div class="metadata-row">
        <span class="metadata-item">
          <mat-icon>person</mat-icon>
          {{ getAuthorName() }}
        </span>
        <span class="metadata-item" *ngIf="blog.submitted_at">
          <mat-icon>schedule</mat-icon>
          {{ getFormattedDate(blog.submitted_at) }}
        </span>
      </div>
      
      <div class="metadata-row">
        <span class="metadata-item">
          <mat-icon>folder</mat-icon>
          {{ getProjectName() }}
        </span>
        <span class="metadata-item">
          <mat-icon>business</mat-icon>
          {{ getCompanyName() }}
        </span>
      </div>
      
      <!-- Scripture Reference -->
      <div class="scripture-section" *ngIf="blog.scripture">
        <mat-icon>book</mat-icon>
        <span class="scripture-text">{{ blog.scripture }}</span>
      </div>
    </div>
  </div>

  <!-- Blog Content -->
  <div class="blog-content">
    <div *ngFor="let block of getContentBlocks()" class="content-block">
      
      <!-- Heading Block -->
      <h2 *ngIf="block.type === 'heading'" class="content-heading">
        {{ block.content }}
      </h2>
      
      <!-- Paragraph Block -->
      <p *ngIf="block.type === 'paragraph'" class="content-paragraph">
        {{ block.content }}
      </p>
      
      <!-- Image Block -->
      <div *ngIf="block.type === 'image'" class="content-image-block">
        <img 
          *ngIf="block.imageUrl" 
          [src]="block.imageUrl" 
          [alt]="block.content || 'Blog image'"
          class="content-image">
        <p *ngIf="block.content" class="image-caption">{{ block.content }}</p>
      </div>
      
      <!-- Quote Block -->
      <blockquote *ngIf="block.type === 'quote'" class="content-quote">
        <mat-icon class="quote-icon">format_quote</mat-icon>
        <p>{{ block.content }}</p>
      </blockquote>
      
      <!-- Code Block -->
      <pre *ngIf="block.type === 'code'" class="content-code"><code>{{ block.content }}</code></pre>
    </div>
  </div>

  <!-- Tags -->
  <div class="tags-section" *ngIf="getTags().length > 0">
    <h3>Tags</h3>
    <mat-chip-set>
      <mat-chip *ngFor="let tag of getTags()">{{ tag }}</mat-chip>
    </mat-chip-set>
  </div>

  <!-- Status Information -->
  <div class="status-section" *ngIf="showMetadata">
    <div class="status-info">
      <span class="status-label">Status:</span>
      <span class="status-badge" [class]="'status-' + blog.status">
        {{ blog.status | titlecase }}
      </span>
    </div>
    
    <!-- Review Information -->
    <div *ngIf="blog.review_comments" class="review-info">
      <h4>Review Comments:</h4>
      <p class="review-comments">{{ blog.review_comments }}</p>
      <div *ngIf="blog.reviewed_by_detail && blog.reviewed_at" class="review-meta">
        <small>
          Reviewed by {{ blog.reviewed_by_detail.full_name || blog.reviewed_by_detail.username }} 
          on {{ getFormattedDate(blog.reviewed_at) }}
        </small>
      </div>
    </div>
  </div>
</div>

<!-- Empty State -->
<div *ngIf="!blog" class="empty-state">
  <mat-icon class="empty-icon">article</mat-icon>
  <h3>No blog to preview</h3>
  <p>Create some content to see the preview</p>
</div>
