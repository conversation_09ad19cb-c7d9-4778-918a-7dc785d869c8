import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';

export interface CurrentUser {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  company?: {
    id: number;
    name: string;
  };
  is_active: boolean;
  date_joined: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly API_BASE = 'http://127.0.0.1:8000/api';
  private currentUserSubject = new BehaviorSubject<CurrentUser | null>(null);
  
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient) {
    // Try to load user from localStorage on service initialization
    this.loadUserFromStorage();
  }

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  /**
   * Get current user profile from backend
   */
  getCurrentUser(): Observable<CurrentUser> {
    return this.http.get<CurrentUser>(
      `${this.API_BASE}/me/`,
      { headers: this.getAuthHeaders() }
    ).pipe(
      tap(user => {
        this.setCurrentUser(user);
      })
    );
  }

  /**
   * Set current user and store in localStorage
   */
  setCurrentUser(user: CurrentUser): void {
    this.currentUserSubject.next(user);
    localStorage.setItem('current_user', JSON.stringify(user));
  }

  /**
   * Get current user from memory
   */
  getCurrentUserValue(): CurrentUser | null {
    return this.currentUserSubject.value;
  }

  /**
   * Load user from localStorage
   */
  private loadUserFromStorage(): void {
    const userStr = localStorage.getItem('current_user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        this.currentUserSubject.next(user);
      } catch (error) {
        console.error('Error parsing user from localStorage:', error);
        localStorage.removeItem('current_user');
      }
    }
  }

  /**
   * Clear user data (for logout)
   */
  clearUser(): void {
    this.currentUserSubject.next(null);
    localStorage.removeItem('current_user');
  }

  /**
   * Get user display name
   */
  getUserDisplayName(user?: CurrentUser): string {
    const currentUser = user || this.getCurrentUserValue();
    if (!currentUser) return 'User';
    
    if (currentUser.first_name && currentUser.last_name) {
      return `${currentUser.first_name} ${currentUser.last_name}`;
    }
    
    return currentUser.username;
  }

  /**
   * Get user initials for avatar
   */
  getUserInitials(user?: CurrentUser): string {
    const currentUser = user || this.getCurrentUserValue();
    if (!currentUser) return 'U';
    
    if (currentUser.first_name && currentUser.last_name) {
      return `${currentUser.first_name[0]}${currentUser.last_name[0]}`.toUpperCase();
    }
    
    return currentUser.username.substring(0, 2).toUpperCase();
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    const user = this.getCurrentUserValue();
    return user?.role === role;
  }

  /**
   * Get user role
   */
  getUserRole(): string {
    const user = this.getCurrentUserValue();
    return user?.role || '';
  }
}
