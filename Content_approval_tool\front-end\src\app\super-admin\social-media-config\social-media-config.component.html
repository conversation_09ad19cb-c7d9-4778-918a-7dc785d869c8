<div class="social-media-config-container">
  <mat-card class="main-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>share</mat-icon>
        Social Media Configuration
      </mat-card-title>
      <mat-card-subtitle>
        Configure social media platform credentials for content publishing
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <!-- Configuration Scope -->
      <div class="scope-section">
        <h3>Configuration Scope</h3>
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Apply Configuration To</mat-label>
          <mat-select [(ngModel)]="configScope" (selectionChange)="onScopeChange()" name="configScope">
            <mat-option value="global">Global (All Companies)</mat-option>
            <mat-option value="company">Specific Company</mat-option>
            <mat-option value="project">Specific Project</mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Company Selection -->
        <mat-form-field 
          *ngIf="configScope === 'company' || configScope === 'project'" 
          appearance="outline" 
          class="full-width">
          <mat-label>Select Company</mat-label>
          <mat-select [(ngModel)]="selectedCompany" (selectionChange)="onCompanyChange()" name="selectedCompany">
            <mat-option *ngFor="let company of companies" [value]="company.id">
              {{ company.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Project Selection -->
        <mat-form-field 
          *ngIf="configScope === 'project'" 
          appearance="outline" 
          class="full-width">
          <mat-label>Select Project</mat-label>
          <mat-select [(ngModel)]="selectedProject" (selectionChange)="onProjectChange()" name="selectedProject">
            <mat-option *ngFor="let project of projects" [value]="project.id">
              {{ project.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Platform Configuration -->
      <div class="platforms-section">
        <h3>Platform Configurations</h3>
        
        <mat-tab-group>
          <mat-tab *ngFor="let platform of platforms" [label]="platform.name">
            <div class="platform-config">
              <!-- Platform Header -->
              <div class="platform-header">
                <div class="platform-info">
                  <mat-icon [class]="'platform-icon platform-' + platform.id">{{ platform.icon }}</mat-icon>
                  <h4>{{ platform.name }} Configuration</h4>
                </div>
                <mat-checkbox 
                  [(ngModel)]="platform.enabled"
                  (change)="onPlatformToggle(platform)"
                  color="primary">
                  Enable {{ platform.name }}
                </mat-checkbox>
              </div>

              <!-- Platform Fields -->
              <div *ngIf="platform.enabled" class="platform-fields">
                <mat-form-field 
                  *ngFor="let field of platform.fields" 
                  appearance="outline" 
                  class="full-width">
                  <mat-label>{{ field.label }}</mat-label>
                  <input 
                    *ngIf="field.type === 'text' || field.type === 'password'"
                    matInput 
                    [type]="field.type"
                    [(ngModel)]="field.value"
                    [placeholder]="field.placeholder"
                    [required]="field.required"
                    [name]="platform.id + '_' + field.key">
                  <textarea 
                    *ngIf="field.type === 'textarea'"
                    matInput 
                    rows="3"
                    [(ngModel)]="field.value"
                    [placeholder]="field.placeholder"
                    [required]="field.required"
                    [name]="platform.id + '_' + field.key">
                  </textarea>
                  <mat-icon matSuffix *ngIf="field.type === 'password'">visibility_off</mat-icon>
                  <mat-hint *ngIf="field.required">Required field</mat-hint>
                </mat-form-field>

                <!-- Test Connection Button -->
                <div class="test-connection">
                  <button 
                    mat-raised-button 
                    color="accent"
                    (click)="onTestConnection(platform)"
                    [disabled]="!isPlatformConfigValid(platform)">
                    <mat-icon>wifi_tethering</mat-icon>
                    Test {{ platform.name }} Connection
                  </button>
                </div>
              </div>

              <!-- Disabled State Message -->
              <div *ngIf="!platform.enabled" class="disabled-message">
                <mat-icon>info</mat-icon>
                <p>{{ platform.name }} is currently disabled. Enable it above to configure credentials.</p>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button 
          mat-raised-button 
          color="primary"
          (click)="onSave()"
          [disabled]="!isFormValid() || isSaving">
          <mat-icon *ngIf="!isSaving">save</mat-icon>
          <mat-progress-spinner 
            *ngIf="isSaving" 
            diameter="20" 
            mode="indeterminate">
          </mat-progress-spinner>
          {{ isSaving ? 'Saving...' : 'Save Configuration' }}
        </button>
        
        <button 
          mat-button 
          color="warn"
          (click)="loadExistingConfigs()">
          <mat-icon>refresh</mat-icon>
          Reset to Saved
        </button>
      </div>

      <!-- Help Section -->
      <div class="help-section">
        <mat-card class="help-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>help</mat-icon>
              Configuration Help
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="help-content">
              <h4>Getting API Credentials:</h4>
              <ul>
                <li><strong>Facebook:</strong> Visit Facebook Developers Console to create an app and get access tokens</li>
                <li><strong>Instagram:</strong> Use Facebook's Graph API to get Instagram Business Account credentials</li>
                <li><strong>LinkedIn:</strong> Create a LinkedIn app in the LinkedIn Developer Portal</li>
              </ul>
              
              <h4>Configuration Scopes:</h4>
              <ul>
                <li><strong>Global:</strong> Applies to all companies and projects</li>
                <li><strong>Company:</strong> Applies only to the selected company</li>
                <li><strong>Project:</strong> Applies only to the selected project</li>
              </ul>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-card-content>
  </mat-card>
</div>
