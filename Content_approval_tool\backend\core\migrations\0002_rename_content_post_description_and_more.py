# Generated by Django 5.2 on 2025-05-09 06:50

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.RenameField(
            model_name='post',
            old_name='content',
            new_name='description',
        ),
        migrations.RemoveField(
            model_name='post',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='post',
            name='reviewer_comment',
        ),
        migrations.AddField(
            model_name='company',
            name='creator',
            field=models.ManyToManyField(blank=True, related_name='projects', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='post',
            name='media',
            field=models.FileField(default='default.jpg', upload_to='uploads/'),
        ),
        migrations.AddField(
            model_name='post',
            name='scheduled_time',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='post',
            name='title',
            field=models.CharField(default=None, max_length=255),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='post',
            name='creator',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='post',
            name='scheduled_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='post',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('posted', 'Posted'), ('rejected', 'Rejected'), ('rework', 'Rework'), ('scheduled', 'Scheduled')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='project',
            name='creators',
            field=models.ManyToManyField(limit_choices_to={'role': 'creator'}, related_name='assigned_projects', to=settings.AUTH_USER_MODEL),
        ),
    ]
