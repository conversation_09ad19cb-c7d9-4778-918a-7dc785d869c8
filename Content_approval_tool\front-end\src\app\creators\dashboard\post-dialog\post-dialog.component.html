<h2 mat-dialog-title>
  <mat-icon>add_circle</mat-icon>
  Create Post for {{ data.date }}
</h2>

<mat-dialog-content class="dialog-content">
  <form [formGroup]="postForm" (ngSubmit)="onSubmit()" class="post-form">

    <!-- Title Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Post Title</mat-label>
      <input matInput
             formControlName="title"
             placeholder="Enter an engaging title for your post"
             maxlength="255">
      <mat-icon matSuffix>title</mat-icon>
      <mat-hint>{{ postForm.get('title')?.value?.length || 0 }}/255</mat-hint>
      <mat-error *ngIf="postForm.get('title')?.hasError('required')">
        Title is required
      </mat-error>
      <mat-error *ngIf="postForm.get('title')?.hasError('minlength')">
        Title must be at least 3 characters long
      </mat-error>
    </mat-form-field>

    <!-- Description Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Description</mat-label>
      <textarea matInput
                formControlName="description"
                rows="4"
                placeholder="Describe your post content, target audience, and key messages"
                maxlength="1000"></textarea>
      <mat-icon matSuffix>description</mat-icon>
      <mat-hint>{{ postForm.get('description')?.value?.length || 0 }}/1000</mat-hint>
      <mat-error *ngIf="postForm.get('description')?.hasError('required')">
        Description is required
      </mat-error>
      <mat-error *ngIf="postForm.get('description')?.hasError('minlength')">
        Description must be at least 10 characters long
      </mat-error>
    </mat-form-field>

    <!-- Project Selection -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Select Project</mat-label>
      <mat-select formControlName="project" placeholder="Choose a project">
        <mat-option *ngFor="let project of assignedProjects" [value]="project.id">
          <div class="project-option">
            <span class="project-name">{{ project.name }}</span>
            <span class="project-company">{{ project.company_name || project.company?.name }}</span>
          </div>
        </mat-option>
      </mat-select>
      <mat-icon matSuffix>folder</mat-icon>
      <mat-error *ngIf="postForm.get('project')?.hasError('required')">
        Please select a project
      </mat-error>
    </mat-form-field>

    <!-- Tags Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Tags (Optional)</mat-label>
      <input matInput
             formControlName="tags"
             placeholder="Enter tags separated by commas (e.g., motivation, inspiration, faith)"
             maxlength="255">
      <mat-icon matSuffix>local_offer</mat-icon>
      <mat-hint>Separate multiple tags with commas</mat-hint>
    </mat-form-field>

    <!-- Content Field -->
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Additional Content (Optional)</mat-label>
      <textarea matInput
                formControlName="content"
                rows="3"
                placeholder="Add any additional content, captions, or notes"
                maxlength="2000"></textarea>
      <mat-icon matSuffix>notes</mat-icon>
      <mat-hint>{{ postForm.get('content')?.value?.length || 0 }}/2000</mat-hint>
    </mat-form-field>

    <!-- Scheduling Section -->
    <div class="scheduling-section">
      <h3>
        <mat-icon>schedule</mat-icon>
        Schedule Your Post
      </h3>

      <div class="date-time-row">
        <!-- Scheduled Date -->
        <mat-form-field appearance="outline" class="date-field">
          <mat-label>Scheduled Date</mat-label>
          <input matInput
                 [matDatepicker]="picker"
                 formControlName="scheduled_date"
                 placeholder="Choose date">
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <mat-error *ngIf="postForm.get('scheduled_date')?.hasError('required')">
            Date is required
          </mat-error>
        </mat-form-field>

        <!-- Scheduled Time -->
        <mat-form-field appearance="outline" class="time-field">
          <mat-label>Scheduled Time</mat-label>
          <input matInput
                 type="time"
                 formControlName="scheduled_time"
                 placeholder="Choose time">
          <mat-icon matSuffix>access_time</mat-icon>
          <mat-error *ngIf="postForm.get('scheduled_time')?.hasError('required')">
            Time is required
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <!-- Aspect Ratio Selection -->
    <div class="aspect-ratio-section">
      <h3>
        <mat-icon>aspect_ratio</mat-icon>
        Post Dimensions
      </h3>
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Aspect Ratio</mat-label>
        <mat-select [(ngModel)]="selectedAspectRatio" (selectionChange)="onAspectRatioChange()" name="aspectRatio">
          <mat-option *ngFor="let ratio of aspectRatios" [value]="ratio.value">
            {{ ratio.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <div class="aspect-ratio-preview">
        <div class="preview-frame" [ngStyle]="getAspectRatioStyle()">
          <span class="preview-text">{{ selectedAspectRatio }} Preview</span>
        </div>
      </div>
    </div>

    <!-- Media Upload Section -->
    <div class="media-upload-section">
      <h3>
        <mat-icon>perm_media</mat-icon>
        Upload Media
      </h3>

      <div class="upload-area">
        <input type="file"
               #fileInput
               (change)="onFileSelected($event)"
               accept="image/*,video/*"
               multiple
               style="display: none;">

        <div class="upload-dropzone" (click)="fileInput.click()">
          <mat-icon class="upload-icon">cloud_upload</mat-icon>
          <p class="upload-text">Click to upload images or videos</p>
          <p class="upload-hint">Supports: JPG, PNG, GIF, MP4, WebM (Max 50MB each)</p>
        </div>
      </div>

      <!-- Selected Files Display -->
      <div *ngIf="selectedFiles.length > 0" class="selected-files">
        <h4>Selected Files ({{ selectedFiles.length }})</h4>
        <mat-chip-set>
          <mat-chip *ngFor="let file of selectedFiles; let i = index"
                    [removable]="true"
                    (removed)="removeFile(i)">
            <mat-icon matChipAvatar>{{ getFileType(file) === 'image' ? 'image' : 'videocam' }}</mat-icon>
            {{ file.name }}
            <span class="file-size">({{ formatFileSize(file.size) }})</span>
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-set>

        <!-- Media Preview with Aspect Ratio -->
        <div class="media-preview-section" *ngIf="selectedFiles.length > 0">
          <h4>Preview with {{ selectedAspectRatio }} aspect ratio:</h4>
          <div class="media-preview-container" [ngStyle]="getAspectRatioStyle()">
            <img *ngIf="getFileType(selectedFiles[0]) === 'image'"
                 [src]="getFilePreviewUrl(selectedFiles[0])"
                 alt="Preview"
                 class="preview-media">
            <video *ngIf="getFileType(selectedFiles[0]) === 'video'"
                   [src]="getFilePreviewUrl(selectedFiles[0])"
                   class="preview-media"
                   controls>
            </video>
          </div>
        </div>
      </div>
    </div>

  </form>
</mat-dialog-content>

<mat-dialog-actions align="end" class="dialog-actions">
  <button mat-button type="button" (click)="onCancel()" [disabled]="isSubmitting">
    <mat-icon>close</mat-icon>
    Cancel
  </button>
  <button mat-raised-button
          color="primary"
          (click)="onSubmit()"
          [disabled]="postForm.invalid || selectedFiles.length === 0 || isSubmitting">
    <mat-spinner *ngIf="isSubmitting" diameter="20"></mat-spinner>
    <mat-icon *ngIf="!isSubmitting">publish</mat-icon>
    {{ isSubmitting ? 'Creating...' : 'Create Post' }}
  </button>
</mat-dialog-actions>
