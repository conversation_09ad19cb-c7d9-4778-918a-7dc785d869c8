.social-media-config-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.main-card {
  margin-bottom: 20px;
}

.scope-section {
  margin-bottom: 32px;
  padding: 24px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.scope-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.platforms-section {
  margin-bottom: 32px;
}

.platforms-section h3 {
  margin: 0 0 24px 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.platform-config {
  padding: 24px 0;
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e0e0e0;
}

.platform-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.platform-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.platform-facebook { color: #1877f2; }
.platform-instagram { color: #e4405f; }
.platform-linkedin { color: #0077b5; }

.platform-info h4 {
  margin: 0;
  color: #333;
}

.platform-fields {
  display: grid;
  gap: 16px;
  margin-bottom: 24px;
}

.full-width {
  width: 100%;
}

.test-connection {
  display: flex;
  justify-content: center;
  margin: 24px 0;
}

.disabled-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px;
  background-color: #f5f5f5;
  border-radius: 8px;
  color: #666;
  text-align: center;
}

.disabled-message mat-icon {
  color: #999;
}

.disabled-message p {
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin: 32px 0;
  padding-top: 24px;
  border-top: 2px solid #e0e0e0;
}

.help-section {
  margin-top: 32px;
}

.help-card {
  background-color: #f8f9fa;
  border-left: 4px solid #28a745;
}

.help-card .mat-mdc-card-title {
  color: #28a745;
}

.help-content h4 {
  margin: 16px 0 8px 0;
  color: #333;
}

.help-content ul {
  margin: 8px 0 16px 20px;
  color: #666;
}

.help-content li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* Material Design Enhancements */
.mat-mdc-card-header {
  background-color: #f5f5f5;
  border-radius: 8px 8px 0 0;
}

.mat-mdc-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mat-mdc-tab-group {
  margin-bottom: 24px;
}

.mat-mdc-tab-body-content {
  overflow: visible !important;
}

/* Form Field Styling */
.mat-mdc-form-field {
  margin-bottom: 16px;
}

.mat-mdc-form-field .mat-mdc-form-field-icon-suffix {
  color: #666;
}

/* Button Styling */
.mat-mdc-raised-button {
  min-width: 140px;
}

.mat-mdc-raised-button .mat-icon {
  margin-right: 8px;
}

/* Progress Spinner in Button */
.mat-mdc-raised-button mat-progress-spinner {
  margin-right: 8px;
}

/* Checkbox Styling */
.mat-mdc-checkbox {
  margin-left: 8px;
}

/* Success and Error States */
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .social-media-config-container {
    padding: 16px;
  }
  
  .platform-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
  }
  
  .scope-section,
  .platform-config {
    padding: 16px;
  }
  
  .test-connection {
    margin: 16px 0;
  }
  
  .test-connection button {
    width: 100%;
  }
}

/* Tab Content Styling */
.mat-mdc-tab-body-wrapper {
  padding-top: 16px;
}

/* Platform-specific styling */
.mat-mdc-tab[aria-selected="true"] .mat-mdc-tab-label-content {
  color: #2196f3;
}

/* Field validation styling */
.mat-mdc-form-field.mat-form-field-invalid .mat-mdc-text-field-wrapper {
  border-color: #f44336;
}

/* Help section responsive */
@media (max-width: 600px) {
  .help-content ul {
    margin-left: 16px;
  }
  
  .help-content li {
    font-size: 0.9rem;
  }
}
