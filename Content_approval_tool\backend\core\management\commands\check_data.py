from django.core.management.base import BaseCommand
from core.models import User, Company, Project, Post

class Command(BaseCommand):
    help = 'Check data relationships for debugging'

    def handle(self, *args, **options):
        self.stdout.write("=== CHECKING DATA RELATIONSHIPS ===\n")
        
        # Check users
        self.stdout.write("=== USERS ===")
        for user in User.objects.all():
            self.stdout.write(f"User: {user.username}, Role: {user.role}, Company: {user.company}")
        
        # Check companies
        self.stdout.write("\n=== COMPANIES ===")
        for company in Company.objects.all():
            self.stdout.write(f"Company: {company.name}, ID: {company.id}")
        
        # Check projects
        self.stdout.write("\n=== PROJECTS ===")
        for project in Project.objects.all():
            creators = [c.username for c in project.creators.all()]
            self.stdout.write(f"Project: {project.name}, Company: {project.company.name}, Creators: {creators}")
        
        # Check posts
        self.stdout.write("\n=== POSTS ===")
        for post in Post.objects.all():
            self.stdout.write(f"Post: {post.title}, Creator: {post.creator.username}, Project: {post.project.name}, Status: {post.status}, Project Company: {post.project.company.name}")
        
        # Check specific filtering for company admin
        self.stdout.write("\n=== COMPANY ADMIN FILTERING TEST ===")
        company_admins = User.objects.filter(role='company_admin')
        for admin in company_admins:
            self.stdout.write(f"\nTesting for Company Admin: {admin.username}")
            if admin.company:
                self.stdout.write(f"Admin Company: {admin.company.name}")
                
                # Get company projects
                company_projects = Project.objects.filter(company=admin.company)
                self.stdout.write(f"Company Projects: {[p.name for p in company_projects]}")
                
                # Get assigned creators
                assigned_creators = User.objects.filter(
                    role='creator',
                    assigned_projects__in=company_projects
                ).distinct()
                self.stdout.write(f"Assigned Creators: {[c.username for c in assigned_creators]}")
                
                # Get pending posts with strict filtering
                pending_strict = Post.objects.filter(
                    project__company=admin.company,
                    creator__in=assigned_creators,
                    status='submitted'
                )
                self.stdout.write(f"Pending Posts (Strict): {pending_strict.count()}")
                
                # Get pending posts with permissive filtering
                pending_permissive = Post.objects.filter(
                    project__company=admin.company,
                    status='submitted'
                )
                self.stdout.write(f"Pending Posts (Permissive): {pending_permissive.count()}")
                
                for post in pending_permissive:
                    is_creator_assigned = post.project.creators.filter(id=post.creator.id).exists()
                    self.stdout.write(f"  - {post.title} by {post.creator.username} (Creator Assigned: {is_creator_assigned})")
            else:
                self.stdout.write("Admin has no company assigned!")
