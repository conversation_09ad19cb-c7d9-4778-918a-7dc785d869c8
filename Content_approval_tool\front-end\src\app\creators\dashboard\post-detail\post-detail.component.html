
<div class="p-6 max-w-3xl mx-auto" *ngIf="post">
  <h2 class="text-2xl font-bold mb-4">{{ post.title }}</h2>

  <div class="mb-4">
    <p><strong>Status:</strong>
      <span [class]="getStatusClass(post.status)">{{ post.status | titlecase }}</span>
    </p>
    <p><strong>Scheduled Date:</strong> {{ post.scheduled_date | date: 'medium' }}</p>
    <p *ngIf="post.project_name"><strong>Project:</strong> {{ post.project_name }}</p>
    <p *ngIf="post.company_name"><strong>Company:</strong> {{ post.company_name }}</p>
  </div>

  <!-- Review Comments Section -->
  <div *ngIf="post.review_comments && (post.status === 'rejected' || post.status === 'rework')"
       class="mb-6 p-4 border-l-4 rounded-r-lg"
       [class.border-red-500]="post.status === 'rejected'"
       [class.bg-red-50]="post.status === 'rejected'"
       [class.border-orange-500]="post.status === 'rework'"
       [class.bg-orange-50]="post.status === 'rework'">
    <h3 class="text-lg font-semibold mb-2 flex items-center">
      <mat-icon class="mr-2" [class.text-red-600]="post.status === 'rejected'" [class.text-orange-600]="post.status === 'rework'">
        {{ post.status === 'rejected' ? 'cancel' : 'edit' }}
      </mat-icon>
      {{ post.status === 'rejected' ? 'Rejection Reason' : 'Changes Requested' }}
    </h3>
    <div class="mb-2" *ngIf="post.reviewed_by_detail && post.reviewed_at">
      <small class="text-gray-600">
        By {{ post.reviewed_by_detail.full_name || post.reviewed_by_detail.username }}
        on {{ post.reviewed_at | date:'medium' }}
      </small>
    </div>
    <div class="whitespace-pre-wrap">{{ post.review_comments }}</div>
  </div>

  <div class="mb-4">
    <p><strong>Description:</strong></p>
    <div class="border p-4 mb-4 whitespace-pre-wrap bg-gray-50 rounded">{{ post.description }}</div>
  </div>

  <!-- Media Display -->
  <div *ngIf="post.media_url" class="mb-6">
    <p><strong>Media:</strong></p>
    <div class="border rounded p-4">
      <img *ngIf="isImage(post.media_url)" [src]="post.media_url" alt="Post media" class="max-w-full h-auto rounded">
      <video *ngIf="isVideo(post.media_url)" [src]="post.media_url" controls class="max-w-full h-auto rounded">
        Your browser does not support the video tag.
      </video>
      <div *ngIf="!isImage(post.media_url) && !isVideo(post.media_url)">
        <a [href]="post.media_url" target="_blank" class="text-blue-600 hover:underline">
          Download File: {{ getFileName(post.media_url) }}
        </a>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="flex gap-4 mt-6">
    <button mat-raised-button color="primary" (click)="editPost()"
            *ngIf="post.status === 'draft' || post.status === 'rework'">
      <mat-icon>edit</mat-icon>
      Edit Post
    </button>

    <button mat-button (click)="goBack()">
      <mat-icon>arrow_back</mat-icon>
      Back to Posts
    </button>
  </div>
</div>
