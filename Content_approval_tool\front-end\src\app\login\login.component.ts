import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { UserService } from '../shared/services/user.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  username = '';
  password = '';
  errorMessage: string | null = null;
  isLoading = false;

  constructor(
    private http: HttpClient,
    private router: Router,
    private userService: UserService
  ) {}

  onSubmit() {
    // Prevent multiple simultaneous login attempts
    if (this.isLoading) {
      return;
    }

    // Basic validation
    if (!this.username.trim() || !this.password.trim()) {
      this.errorMessage = 'Please enter both username and password.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;
    const loginData = { username: this.username.trim(), password: this.password };

    console.log('🔐 Attempting login for user:', this.username);

    this.http.post<any>('http://127.0.0.1:8000/api/token/', loginData).subscribe({
      next: (res) => {
        this.isLoading = false;
        console.log('✅ Login response received:', res);

        if (res.access && res.refresh && res.user) {
          // Store tokens
          localStorage.setItem('access_token', res.access);
          localStorage.setItem('refresh_token', res.refresh);

          // Store user information
          this.userService.setCurrentUser(res.user);

          console.log('✅ Login successful for user:', res.user.username, 'Role:', res.user.role);

          // Navigate based on role
          const userRole = res.user.role;
          if (userRole === 'super_admin') {
            this.router.navigate(['/super-admin']);
          } else if (userRole === 'company_admin') {
            this.router.navigate(['/company-admin']);
          } else if (userRole === 'creator') {
            this.router.navigate(['/creators']);
          } else {
            this.errorMessage = 'Unknown role, access denied!';
            console.error('❌ Unknown role:', userRole);
          }
        } else {
          this.errorMessage = 'Invalid login response!';
          console.error('❌ Invalid response structure:', res);
        }
      },
      error: (err) => {
        this.isLoading = false;
        console.error('❌ Login error:', err);

        if (err.status === 401) {
          this.errorMessage = 'Invalid username or password. Please try again.';
        } else if (err.status === 0) {
          this.errorMessage = 'Unable to connect to server. Please check your connection.';
        } else {
          this.errorMessage = 'Login failed! Please try again later.';
        }
      }
    });
  }

  getRoleFromToken(token: string): string | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.role || null; // Ensure your Django backend includes the 'role' in the JWT payload
    } catch (e) {
      return null;
    }
  }
}
