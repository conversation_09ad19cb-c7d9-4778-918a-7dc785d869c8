import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule as MatFabModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { FormsModule } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { CalendarViewService } from './calendar-view.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CalendarDay, CalendarPost, CalendarProject, PostStatus, StatusConfig, BlogStatus, CalendarEvent } from './calendar-view.models';
import { PostDialogComponent } from '../post-dialog/post-dialog.component';

@Component({
  selector: 'app-creator-dashboard-calendar-view',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    MatFabModule,
    MatDialogModule,
    FormsModule
  ],
  templateUrl: './creator-dashboard-calendar-view.component.html',
  styleUrls: ['./creator-dashboard-calendar-view.component.css']
})
export class CreatorDashboardCalendarViewComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  calendarDays$: Observable<CalendarDay[]>;
  currentDate$: Observable<Date>;
  selectedDate$: Observable<Date | null>;
  
  searchTerm = '';
  selectedStatuses: (PostStatus | BlogStatus)[] = ['draft', 'submitted', 'approved', 'rejected', 'changes_requested', 'posted', 'rework', 'scheduled'];
  
  readonly weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  readonly statusOptions: (PostStatus | BlogStatus)[] = ['posted', 'scheduled', 'rework', 'draft', 'submitted', 'approved', 'rejected', 'changes_requested'];

  constructor(
    private calendarService: CalendarViewService,
    private creatorDashboardService: CreatorDashboardService,
    private dialog: MatDialog
  ) {
    this.calendarDays$ = new Observable<CalendarDay[]>();
    this.currentDate$ = this.calendarService.currentDate$;
    this.selectedDate$ = this.calendarService.selectedDate$;
  }

  ngOnInit(): void {
    // Load creator dashboard data first
    this.creatorDashboardService.refreshDashboardData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          console.log('Dashboard data loaded:', data);
          // Refresh calendar after data is loaded
          this.refreshCalendarData();
        },
        error: (error) => {
          console.error('Error loading dashboard data:', error);
        }
      });

    // Initialize calendar days observable
    this.currentDate$
      .pipe(takeUntil(this.destroy$))
      .subscribe(date => {
        this.calendarDays$ = this.calendarService.generateCalendarDays(date);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Navigation methods
  navigateToPreviousMonth(): void {
    this.calendarService.navigateToPreviousMonth();
  }

  navigateToNextMonth(): void {
    this.calendarService.navigateToNextMonth();
  }

  navigateToToday(): void {
    this.calendarService.navigateToToday();
  }

  onDatePickerChange(date: Date): void {
    this.calendarService.navigateToMonth(date);
  }

  // Day selection
  onDayClick(day: CalendarDay): void {
    this.calendarService.selectDate(day.date);
    
    // If day has no events, open create post dialog
    if (day.events.length === 0) {
      this.openCreatePostDialog(day.date);
    }
  }

  onPostClick(event: Event, item: CalendarEvent): void {
    event.stopPropagation();
    // Handle click based on item type (post or blog)
    if (item.type === 'post') {
      console.log('Post clicked:', item);
      // TODO: Open post detail or edit dialog
    } else if (item.type === 'blog') {
      console.log('Blog clicked:', item);
      // TODO: Open blog detail or review dialog
    }
  }

  onThumbnailClick(eventItem: CalendarEvent, event: Event): void {
    event.stopPropagation();
    // Open thumbnail preview modal or navigate to content detail
    console.log('Thumbnail clicked:', eventItem);
    // TODO: Implement thumbnail preview modal
  }

  // Post creation
  openCreatePostDialog(date?: Date): void {
    let selectedDate = date || new Date();

    // Get current selected date from service if no date provided
    if (!date) {
      this.calendarService.selectedDate$.pipe(takeUntil(this.destroy$)).subscribe(currentSelected => {
        if (currentSelected) {
          selectedDate = currentSelected;
        }
      });
    }

    // Get assigned projects for the dialog
    this.creatorDashboardService.assignedProjects$.pipe(takeUntil(this.destroy$)).subscribe(projects => {
      const dialogRef = this.dialog.open(PostDialogComponent, {
        width: '600px',
        data: {
          date: selectedDate.toISOString().split('T')[0],
          assignedProjects: projects
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // Refresh calendar data
          this.refreshCalendarData();
        }
      });
    });
  }

  // Filtering
  onSearchChange(): void {
    this.calendarService.updateFilter({ searchTerm: this.searchTerm });
  }

  onStatusFilterChange(status: PostStatus | BlogStatus): void {
    const index = this.selectedStatuses.indexOf(status);
    if (index > -1) {
      this.selectedStatuses.splice(index, 1);
    } else {
      this.selectedStatuses.push(status);
    }
    this.calendarService.updateFilter({ statuses: [...this.selectedStatuses] });
  }

  isStatusSelected(status: PostStatus | BlogStatus): boolean {
    return this.selectedStatuses.includes(status);
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatuses = ['draft', 'submitted', 'approved', 'rejected', 'changes_requested', 'posted', 'rework', 'scheduled'];
    this.calendarService.updateFilter({ 
      searchTerm: '', 
      statuses: [...this.selectedStatuses] 
    });
  }

  // Utility methods
  getStatusConfig(status: PostStatus | BlogStatus): StatusConfig {
    switch (status) {
      case 'posted': return { color: '#673ab7', icon: 'done_all', label: 'Posted' }; // Deep Purple
      case 'scheduled': return { color: '#00bcd4', icon: 'event', label: 'Scheduled' }; // Cyan
      case 'rework': return { color: '#ff5722', icon: 'edit_note', label: 'Rework' }; // Deep Orange (for posts)
      case 'draft': return { color: '#9e9e9e', icon: 'create', label: 'Draft' }; // Grey
      case 'submitted': return { color: '#ff9800', icon: 'send', label: 'Submitted' }; // Orange
      case 'approved': return { color: '#4caf50', icon: 'check_circle', label: 'Approved' }; // Green
      case 'rejected': return { color: '#f44336', icon: 'cancel', label: 'Rejected' }; // Red
      case 'changes_requested': return { color: '#2196f3', icon: 'info', label: 'Changes Requested' }; // Blue (for blogs)
      default: return { color: '#9e9e9e', icon: 'help_outline', label: 'Unknown' };
    }
  }

  formatMonthYear(date: Date): string {
    return this.calendarService.formatMonthYear(date);
  }

  getTruncatedTitle(title: string, maxLength: number = 20): string {
    return title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
  }

  getPostTooltip(event: any): string {
    let tooltip = `${event.title}\nStatus: ${event.status}`;
    if (event.type === 'post') {
      tooltip += `\n${event.description}\nScheduled: ${event.time}`;
    } else if (event.type === 'blog') {
      tooltip += `\nAuthor: ${event.author_name || 'N/A'}`;
      tooltip += `\nSubmitted: ${event.submitted_at ? (new Date(event.submitted_at)).toLocaleDateString() : 'N/A'}`;
    }

    if (event.projectTitle) {
      tooltip += `\nProject: ${event.projectTitle}`;
    }
    if (event.companyName) {
      tooltip += `\nCompany: ${event.companyName}`;
    }
    if (event.deadline) {
      tooltip += `\nDeadline: ${new Date(event.deadline).toLocaleDateString()}`;
    }
    if (event.review_comments) {
      tooltip += `\nReview Comments: ${event.review_comments}`;
    }

    return tooltip;
  }

  private refreshCalendarData(): void {
    this.calendarService.currentDate$.pipe(takeUntil(this.destroy$)).subscribe(currentDate => {
      this.calendarDays$ = this.calendarService.generateCalendarDays(currentDate);
    });
  }

  // Track by functions for performance
  trackByDate(index: number, day: CalendarDay): string {
    return day.date.toISOString();
  }

  trackByEventId(index: number, event: any): string {
    return event.id;
  }
}
