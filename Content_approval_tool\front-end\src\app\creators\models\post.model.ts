export interface Post {
  id?: number;
  title: string;
  description: string;                    // corresponds to `description` in Django
  media_url?: string;      
  media_width?: number;
  media_height?: number;                   // URL to image/video file from Django
  scheduled_date?: string;               // should match Django field
  scheduled_time?: string;               // if used on frontend
  status: 'draft' | 'submitted' | 'posted' | 'rejected' | 'rework' | 'scheduled';
  creator?: number | string;             // ID or username
  project: number;                      // project ID (for upload)
  created_at?: string;

  // Review fields from company admin
  review_comments?: string;              // Comments/feedback from company admin
  reviewed_by?: number;                  // ID of company admin who reviewed
  reviewed_at?: string;                  // Timestamp of review
  reviewed_by_detail?: {                 // Full reviewer details
    id: number;
    username: string;
    full_name: string;
  };

  // Additional fields from backend serializer
  project_name?: string;                 // Project name from backend
  creator_name?: string;                 // Creator name from backend
  company_name?: string;                 // Company name from project
  project_deadline?: string;             // Project deadline
  project_detail?: {                     // Full project details
    id: number;
    name: string;
    title?: string;
    company_detail: {
      id: number;
      name: string;
      logo?: string;
    };
  };
  mediaType?: string;
}
