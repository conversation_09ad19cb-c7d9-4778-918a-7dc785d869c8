# Generated migration for adding 'rework' status to Blog model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0014_blog_media_dimensions'),
    ]

    operations = [
        migrations.AlterField(
            model_name='blog',
            name='status',
            field=models.CharField(
                choices=[
                    ('draft', 'Draft'),
                    ('submitted', 'Submitted'),
                    ('approved', 'Approved'),
                    ('rejected', 'Rejected'),
                    ('changes_requested', 'Changes Requested'),
                    ('rework', 'Rework'),
                    ('posted', 'Posted'),
                    ('scheduled', 'Scheduled')
                ],
                default='draft',
                max_length=20
            ),
        ),
    ]
