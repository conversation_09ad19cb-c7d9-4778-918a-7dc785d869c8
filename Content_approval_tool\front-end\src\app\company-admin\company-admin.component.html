<!-- Header Toolbar -->
<mat-toolbar color="primary" class="dashboard-header">
  <span class="header-title">
    <mat-icon>business</mat-icon>
    Company Admin Dashboard
  </span>
  <span class="spacer"></span>
  <button mat-icon-button (click)="loadDashboardData()" matTooltip="Refresh">
    <mat-icon>refresh</mat-icon>
  </button>
  <button mat-button (click)="logout()">
    <mat-icon>logout</mat-icon>
    Logout
  </button>
</mat-toolbar>

<!-- Loading Spinner -->
<div *ngIf="isLoading" class="loading-container">
  <mat-spinner></mat-spinner>
  <p>Loading dashboard...</p>
</div>

<!-- Main Dashboard Content -->
<div *ngIf="!isLoading" class="dashboard-container">

  <!-- Welcome Section -->
  <mat-card class="welcome-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>waving_hand</mat-icon>
        Welcome back, {{ dashboardStats?.admin_name }}!
      </mat-card-title>
      <mat-card-subtitle>
        {{ dashboardStats?.company_name }} • {{ currentDate | date:'fullDate' }}
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>Manage your projects, review content, and oversee your team's work.</p>
    </mat-card-content>
  </mat-card>

  <!-- Tabbed Interface -->
  <mat-card class="main-content-card">
    <mat-tab-group [(selectedIndex)]="selectedTabIndex" class="dashboard-tabs">

      <!-- Dashboard Overview Tab -->
      <mat-tab label="Dashboard Overview">
        <div class="tab-content">

          <!-- Project Summary Panel -->
  <div class="stats-grid">
    <mat-card class="stat-card projects-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>folder</mat-icon>
          Projects Overview
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalProjects() }}</span>
          <span class="stat-label">Total Projects</span>
        </div>
        <div class="stat-item">
          <span class="stat-number active">{{ getActiveProjects() }}</span>
          <span class="stat-label">Active Projects</span>
        </div>
        <div class="stat-item">
          <span class="stat-number completed">{{ getCompletedProjects() }}</span>
          <span class="stat-label">Completed Projects</span>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>article</mat-icon>
          Content Statistics
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalPosts() }}</span>
          <span class="stat-label">Total Posts</span>
        </div>
        <div class="stat-item">
          <span class="stat-number pending" [matBadge]="getPendingReviews()"
                matBadgeColor="warn" [matBadgeHidden]="getPendingReviews() === 0">
            {{ getPendingReviews() }}
          </span>
          <span class="stat-label">Pending Reviews</span>
        </div>
        <div class="stat-item">
          <span class="stat-number approved">{{ getApprovedPosts() }}</span>
          <span class="stat-label">Approved</span>
        </div>
        <div class="stat-item">
          <span class="stat-number rejected">{{ getRejectedPosts() }}</span>
          <span class="stat-label">Rejected</span>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card creators-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>people</mat-icon>
          Content Creators
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="stat-item">
          <span class="stat-number">{{ getTotalCreators() }}</span>
          <span class="stat-label">Total Creators</span>
        </div>
        <div class="stat-item">
          <span class="stat-number active">{{ getActiveCreatorsCount() }}</span>
          <span class="stat-label">Active Creators</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Content Review Activity -->
  <div class="content-section">
    <!-- Pending Reviews -->
    <mat-card class="content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>pending_actions</mat-icon>
          Pending Content Reviews
          <mat-chip-set *ngIf="pendingReviews.length > 0">
            <mat-chip color="warn">{{ pendingReviews.length }}</mat-chip>
          </mat-chip-set>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="pendingReviews.length === 0" class="empty-state">
          <mat-icon>check_circle</mat-icon>
          <p>No pending reviews! All caught up.</p>
        </div>

        <div *ngIf="pendingReviews.length > 0" class="content-list">
          <div *ngFor="let item of pendingReviews" class="content-item">
            <div class="content-info">
              <div class="content-header">
                <h4>
                  <mat-icon *ngIf="isBlog(item)" color="accent">library_books</mat-icon>
                  <mat-icon *ngIf="isPost(item)" color="primary">article</mat-icon>
                  {{ item.title }}
                  <span class="type-label" *ngIf="isBlog(item)">[Blog]</span>
                  <span class="type-label" *ngIf="isPost(item)">[Post]</span>
                </h4>
                <mat-chip [style.background-color]="getStatusColor(item.status)">
                  <mat-icon>{{ getStatusIcon(item.status) }}</mat-icon>
                  {{ item.status | titlecase }}
                </mat-chip>
              </div>
              <p class="content-description">{{ isBlog(item) ? item.content : item.description }}</p>
              <div class="content-meta">
                <span><mat-icon>person</mat-icon> {{ isBlog(item) ? (item.author_name || 'Unknown') : (item.creator_name || 'Unknown') }}</span>
                <span><mat-icon>folder</mat-icon> {{ isBlog(item) ? (item.project_detail?.title || item.project_detail?.name || 'Unknown') : (item.project_name || 'Unknown') }}</span>
                <span><mat-icon>schedule</mat-icon> {{ getSubmittedDate(item) }}</span>
              </div>
            </div>
            <!-- Media Preview for posts and blogs -->
            <div *ngIf="isPost(item) && item.media_url" class="media-preview">
              <img *ngIf="isImage(item.media_url)" [src]="item.media_url" alt="Content preview" class="content-image">
              <video *ngIf="isVideo(item.media_url)" [src]="item.media_url" controls class="content-video"></video>
              <div *ngIf="!isImage(item.media_url) && !isVideo(item.media_url)" class="content-file">
                <mat-icon>attach_file</mat-icon>
                <span>{{ getFileName(item.media_url) }}</span>
              </div>
            </div>
            <div *ngIf="isBlog(item) && item.cover_image" class="media-preview">
              <img [src]="item.cover_image" alt="Blog Cover" class="content-image">
            </div>
            <!-- Action Buttons -->
            <div class="action-buttons">
              <button mat-raised-button color="basic" (click)="openContentPreviewDialog(item)">
                <mat-icon>visibility</mat-icon> Preview
              </button>
              <ng-container *ngIf="isPost(item)">
                <button mat-raised-button color="primary" (click)="approvePost(item.id)">
                  <mat-icon>check</mat-icon> Approve
                </button>
                <button mat-raised-button color="accent" (click)="requestChangesPost(item.id)">
                  <mat-icon>edit</mat-icon> Request Changes
                </button>
                <button mat-raised-button color="warn" (click)="rejectPost(item.id)">
                  <mat-icon>close</mat-icon> Reject
                </button>
              </ng-container>
              <ng-container *ngIf="isBlog(item)">
                <button mat-raised-button color="primary" (click)="openBlogReviewDialog('approve', item)">
                  <mat-icon>check</mat-icon> Approve
                </button>
                <button mat-raised-button color="accent" (click)="openBlogReviewDialog('changes', item)">
                  <mat-icon>edit</mat-icon> Request Changes
                </button>
                <button mat-raised-button color="warn" (click)="openBlogReviewDialog('reject', item)">
                  <mat-icon>close</mat-icon> Reject
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Recent Content Activity -->
    <mat-card class="content-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>history</mat-icon>
          Recent Content Activity
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="recentContent.length === 0" class="empty-state">
          <mat-icon>article</mat-icon>
          <p>No recent content activity.</p>
        </div>
        <div *ngIf="recentContent.length > 0" class="content-list">
          <div *ngFor="let item of recentContent" class="content-item">
            <div class="content-info">
              <div class="content-header">
                <h4>
                  <mat-icon *ngIf="isBlog(item)" color="accent">library_books</mat-icon>
                  <mat-icon *ngIf="isPost(item)" color="primary">article</mat-icon>
                  {{ item.title }}
                  <span class="type-label" *ngIf="isBlog(item)">[Blog]</span>
                  <span class="type-label" *ngIf="isPost(item)">[Post]</span>
                </h4>
                <mat-chip [style.background-color]="getStatusColor(item.status)">
                  <mat-icon>{{ getStatusIcon(item.status) }}</mat-icon>
                  {{ item.status | titlecase }}
                </mat-chip>
              </div>
              <p class="content-description">{{ isBlog(item) ? item.content : item.description }}</p>
              <div class="content-meta">
                <span><mat-icon>person</mat-icon> {{ isBlog(item) ? (item.author_name || 'Unknown') : (item.creator_name || 'Unknown') }}</span>
                <span><mat-icon>folder</mat-icon> {{ isBlog(item) ? (item.project_detail?.title || item.project_detail?.name || 'Unknown') : (item.project_name || 'Unknown') }}</span>
                <span><mat-icon>schedule</mat-icon> {{ getSubmittedDate(item) }}</span>
              </div>
            </div>
            <!-- Media Preview for posts and blogs -->
            <div *ngIf="isPost(item) && item.media_url" class="media-preview">
              <img *ngIf="isImage(item.media_url)" [src]="item.media_url" alt="Content preview" class="content-image">
              <video *ngIf="isVideo(item.media_url)" [src]="item.media_url" controls class="content-video"></video>
              <div *ngIf="!isImage(item.media_url) && !isVideo(item.media_url)" class="content-file">
                <mat-icon>attach_file</mat-icon>
                <span>{{ getFileName(item.media_url) }}</span>
              </div>
            </div>
            <div *ngIf="isBlog(item) && item.cover_image" class="media-preview">
              <img [src]="item.cover_image" alt="Blog Cover" class="content-image">
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

  </div>

          <!-- Action Shortcuts -->
          <mat-card class="action-shortcuts-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>flash_on</mat-icon>
                Quick Actions
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="action-grid">
                <button mat-raised-button color="primary" (click)="loadDashboardData()">
                  <mat-icon>refresh</mat-icon>
                  Refresh Dashboard
                </button>
                <button mat-raised-button color="accent"
                        [disabled]="getPendingReviews() === 0">
                  <mat-icon>rate_review</mat-icon>
                  Review All Pending ({{ getPendingReviews() }})
                </button>
                <button mat-raised-button color="primary" (click)="viewAnalytics()">
                  <mat-icon>analytics</mat-icon>
                  View Analytics
                </button>
                <button mat-raised-button>
                  <mat-icon>settings</mat-icon>
                  Settings
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Project Management Tab -->
      <mat-tab label="Project Management">
        <div class="tab-content">
          <div class="projects-header">
            <h2>
              <mat-icon>folder_open</mat-icon>
              My Assigned Projects
            </h2>
            <p>Manage projects, assign creators, and track progress</p>
          </div>

          <div *ngIf="assignedProjects.length === 0" class="empty-state">
            <mat-icon>folder_off</mat-icon>
            <h3>No Projects Assigned</h3>
            <p>You don't have any projects assigned yet. Contact your super admin to get started.</p>
          </div>

          <div *ngIf="assignedProjects.length > 0" class="projects-container">
            <mat-accordion class="projects-accordion">
              <mat-expansion-panel *ngFor="let project of assignedProjects" class="project-panel">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <mat-icon>folder</mat-icon>
                    <span class="project-title">{{ project.title || project.name }}</span>
                  </mat-panel-title>
                  <mat-panel-description>
                    <mat-chip-set>
                      <mat-chip>{{ project.creators.length }} Creators</mat-chip>
                      <mat-chip>{{ project.posts_count }} Posts</mat-chip>
                      <mat-chip *ngIf="project.pending_posts > 0" color="warn">
                        {{ project.pending_posts }} Pending
                      </mat-chip>
                    </mat-chip-set>
                    <span class="deadline" *ngIf="project.deadline">
                      <mat-icon>schedule</mat-icon>
                      {{ project.deadline | date:'shortDate' }}
                    </span>
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="project-details">
                  <div class="project-info">
                    <h4>Project Information</h4>
                    <p *ngIf="project.description">{{ project.description }}</p>
                    <div class="project-meta">
                      <span><strong>Company:</strong> {{ project.company.name }}</span>
                      <span *ngIf="project.deadline"><strong>Deadline:</strong> {{ project.deadline | date:'fullDate' }}</span>
                      <span><strong>Created:</strong> {{ project.created_at | date:'shortDate' }}</span>
                    </div>
                  </div>

                  <div class="creators-section">
                    <div class="section-header">
                      <h4>
                        <mat-icon>people</mat-icon>
                        Assigned Creators ({{ project.creators.length }})
                      </h4>
                      <button mat-raised-button color="primary" (click)="openCreatorAssignmentDialog(project.id)">
                        <mat-icon>person_add</mat-icon>
                        Manage Creators
                      </button>
                    </div>

                    <div *ngIf="project.creators.length === 0" class="empty-creators">
                      <mat-icon>person_off</mat-icon>
                      <p>No creators assigned to this project yet.</p>
                    </div>

                    <div *ngIf="project.creators.length > 0" class="creators-grid">
                      <mat-card *ngFor="let creator of project.creators" class="creator-card">
                        <mat-card-header>
                          <mat-card-title>{{ creator.full_name }}</mat-card-title>
                          <mat-card-subtitle>{{ creator.email }}</mat-card-subtitle>
                        </mat-card-header>
                        <mat-card-content>
                          <div class="creator-stats">
                            <span class="stat">
                              <mat-icon>article</mat-icon>
                              {{ creator.total_posts || 0 }} Posts
                            </span>
                            <span class="stat" *ngIf="creator.pending_posts > 0">
                              <mat-icon>pending</mat-icon>
                              {{ creator.pending_posts }} Pending
                            </span>
                          </div>
                        </mat-card-content>
                      </mat-card>
                    </div>
                  </div>
                </div>
              </mat-expansion-panel>
            </mat-accordion>
          </div>
        </div>
      </mat-tab>

    </mat-tab-group>
  </mat-card>

</div>