import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BlogCreateComponent } from './blog-create.component';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

describe('BlogCreateComponent', () => {
  let component: BlogCreateComponent;
  let fixture: ComponentFixture<BlogCreateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BlogCreateComponent ],
      imports: [ FormsModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule ]
    }).compileComponents();

    fixture = TestBed.createComponent(BlogCreateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create blog component', () => {
    expect(component).toBeTruthy();
  });
});
