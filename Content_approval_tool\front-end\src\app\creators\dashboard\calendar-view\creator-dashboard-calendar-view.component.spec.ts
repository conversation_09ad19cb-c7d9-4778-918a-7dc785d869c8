import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { CreatorDashboardCalendarViewComponent } from './creator-dashboard-calendar-view.component';
import { CalendarViewService } from './calendar-view.service';
import { CalendarDay } from './calendar-view.models';

describe('CreatorDashboardCalendarViewComponent', () => {
  let component: CreatorDashboardCalendarViewComponent;
  let fixture: ComponentFixture<CreatorDashboardCalendarViewComponent>;
  let mockCalendarService: jasmine.SpyObj<CalendarViewService>;
  let mockDialog: jasmine.SpyObj<MatDialog>;

  const mockCalendarDays: CalendarDay[] = [
    {
      date: new Date(2024, 0, 1),
      isCurrentMonth: true,
      isToday: false,
      isSelected: false,
      posts: []
    },
    {
      date: new Date(2024, 0, 2),
      isCurrentMonth: true,
      isToday: true,
      isSelected: false,
      posts: [
        {
          id: 1,
          title: 'Test Post',
          description: 'Test Description',
          time: '10:00 AM',
          status: 'draft',
          mediaType: 'image',
          scheduledDate: new Date(2024, 0, 2)
        }
      ]
    }
  ];

  beforeEach(async () => {
    const calendarServiceSpy = jasmine.createSpyObj('CalendarViewService', [
      'navigateToPreviousMonth',
      'navigateToNextMonth',
      'navigateToToday',
      'navigateToMonth',
      'selectDate',
      'updateFilter',
      'generateCalendarDays',
      'getStatusConfig',
      'formatMonthYear'
    ], {
      currentDate$: of(new Date(2024, 0, 1)),
      selectedDate$: of(null),
      filter$: of({ statuses: ['draft'], searchTerm: '', tags: [] })
    });

    const dialogSpy = jasmine.createSpyObj('MatDialog', ['open']);

    await TestBed.configureTestingModule({
      imports: [
        CreatorDashboardCalendarViewComponent,
        NoopAnimationsModule
      ],
      providers: [
        { provide: CalendarViewService, useValue: calendarServiceSpy },
        { provide: MatDialog, useValue: dialogSpy }
      ]
    }).compileComponents();

    mockCalendarService = TestBed.inject(CalendarViewService) as jasmine.SpyObj<CalendarViewService>;
    mockDialog = TestBed.inject(MatDialog) as jasmine.SpyObj<MatDialog>;
    
    mockCalendarService.generateCalendarDays.and.returnValue(of(mockCalendarDays));
    mockCalendarService.getStatusConfig.and.returnValue({
      color: '#9e9e9e',
      icon: 'draft',
      label: 'Draft'
    });
    mockCalendarService.formatMonthYear.and.returnValue('January 2024');

    fixture = TestBed.createComponent(CreatorDashboardCalendarViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.searchTerm).toBe('');
    expect(component.selectedStatuses).toEqual(['draft', 'submitted', 'posted', 'rejected', 'rework', 'scheduled']);
    expect(component.weekDays).toEqual(['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']);
  });

  it('should call navigateToPreviousMonth when previous button clicked', () => {
    component.navigateToPreviousMonth();
    expect(mockCalendarService.navigateToPreviousMonth).toHaveBeenCalled();
  });

  it('should call navigateToNextMonth when next button clicked', () => {
    component.navigateToNextMonth();
    expect(mockCalendarService.navigateToNextMonth).toHaveBeenCalled();
  });

  it('should call navigateToToday when today button clicked', () => {
    component.navigateToToday();
    expect(mockCalendarService.navigateToToday).toHaveBeenCalled();
  });

  it('should select date and open create dialog when empty day clicked', () => {
    const emptyDay = mockCalendarDays[0]; // Day with no posts
    spyOn(component, 'openCreatePostDialog');
    
    component.onDayClick(emptyDay);
    
    expect(mockCalendarService.selectDate).toHaveBeenCalledWith(emptyDay.date);
    expect(component.openCreatePostDialog).toHaveBeenCalledWith(emptyDay.date);
  });

  it('should select date but not open dialog when day with posts clicked', () => {
    const dayWithPosts = mockCalendarDays[1]; // Day with posts
    spyOn(component, 'openCreatePostDialog');
    
    component.onDayClick(dayWithPosts);
    
    expect(mockCalendarService.selectDate).toHaveBeenCalledWith(dayWithPosts.date);
    expect(component.openCreatePostDialog).not.toHaveBeenCalled();
  });

  it('should update search filter when search term changes', () => {
    component.searchTerm = 'test';
    component.onSearchChange();
    
    expect(mockCalendarService.updateFilter).toHaveBeenCalledWith({ searchTerm: 'test' });
  });

  it('should toggle status filter when status chip clicked', () => {
    // Remove a status
    component.onStatusFilterChange('draft');
    expect(component.selectedStatuses).not.toContain('draft');
    expect(mockCalendarService.updateFilter).toHaveBeenCalled();
    
    // Add it back
    component.onStatusFilterChange('draft');
    expect(component.selectedStatuses).toContain('draft');
  });

  it('should clear all filters', () => {
    component.searchTerm = 'test';
    component.selectedStatuses = ['draft'];
    
    component.clearFilters();
    
    expect(component.searchTerm).toBe('');
    expect(component.selectedStatuses).toEqual(['draft', 'submitted', 'posted', 'rejected', 'rework', 'scheduled']);
    expect(mockCalendarService.updateFilter).toHaveBeenCalledWith({
      searchTerm: '',
      statuses: ['draft', 'submitted', 'posted', 'rejected', 'rework', 'scheduled']
    });
  });

  it('should truncate long titles', () => {
    const longTitle = 'This is a very long title that should be truncated';
    const truncated = component.getTruncatedTitle(longTitle, 10);
    expect(truncated).toBe('This is a ...');
  });

  it('should not truncate short titles', () => {
    const shortTitle = 'Short';
    const result = component.getTruncatedTitle(shortTitle, 10);
    expect(result).toBe('Short');
  });

  it('should open create post dialog', () => {
    const dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['afterClosed']);
    dialogRefSpy.afterClosed.and.returnValue(of(null));
    mockDialog.open.and.returnValue(dialogRefSpy);
    
    const testDate = new Date(2024, 0, 15);
    component.openCreatePostDialog(testDate);
    
    expect(mockDialog.open).toHaveBeenCalled();
  });
});
