import { Component, OnInit } from '@angular/core';
import { Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { PostService } from '../../services/post.service';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormsModule } from '@angular/forms';
import { CalendarProject } from '../calendar-view/calendar-view.models';
import { Post } from '../../models/post.model';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';



@Component({
  selector: 'app-create-post',
  templateUrl: './create-post.component.html',
  styleUrls: ['./create-post.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatChipsModule
  ],
})
export class CreatePostComponent implements OnInit {
  postForm!: FormGroup;
  selectedFiles: File[] = [];
  assignedProjects: CalendarProject[] = [];
  isSubmitting = false;
  isLoading = false;
  isEditMode = false;
  editingPost: Post | null = null;
  postId: number | null = null;

  // Aspect ratio selection
  selectedAspectRatio: string = '1:1';
  aspectRatios = [
    { value: '1:1', label: 'Square (1:1)', width: 400, height: 400 },
    { value: '16:9', label: 'Landscape (16:9)', width: 400, height: 225 },
    { value: '4:3', label: 'Standard (4:3)', width: 400, height: 300 },
    { value: '9:16', label: 'Portrait (9:16)', width: 225, height: 400 }
  ];
  previewDimensions = { width: 400, height: 400 };

  constructor(
    private fb: FormBuilder,
    private postService: PostService,
    public router: Router,
    private route: ActivatedRoute,
    private creatorDashboardService: CreatorDashboardService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadAssignedProjects();

    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.postId = +params['id'];
        this.isEditMode = true;
        this.loadPostForEditing(this.postId);
      }
    });
  }

  private initializeForm(): void {
    this.postForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      project: ['', Validators.required],
      scheduled_date: ['', Validators.required],
      scheduled_time: ['', Validators.required]
    });
  }

  private loadAssignedProjects(): void {
    this.isLoading = true;
    this.creatorDashboardService.getAssignedProjects().subscribe({
      next: (projects) => {
        this.assignedProjects = projects;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading projects:', error);
        this.showError('Failed to load assigned projects');
        this.isLoading = false;
      }
    });
  }

  private loadPostForEditing(postId: number): void {
    this.isLoading = true;
    this.postService.getPost(postId).subscribe({
      next: (post) => {
        this.editingPost = post;
        this.populateFormWithPost(post);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading post for editing:', error);
        this.showError('Failed to load post for editing');
        this.isLoading = false;
      }
    });
  }

  private populateFormWithPost(post: Post): void {
    // Parse the scheduled date and time
    let scheduledDate = '';
    let scheduledTime = '';

    if (post.scheduled_time) {
      const date = new Date(post.scheduled_time);
      scheduledDate = date.toISOString().split('T')[0]; // YYYY-MM-DD
      scheduledTime = date.toTimeString().split(' ')[0].substring(0, 5); // HH:MM
    }

    this.postForm.patchValue({
      title: post.title,
      description: post.description,
      project: post.project,
      scheduled_date: scheduledDate,
      scheduled_time: scheduledTime
    });
  }


  onFileSelected(event: any): void {
    const files = Array.from(event.target.files) as File[];
    this.selectedFiles = files;

    // Validate file types and sizes
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'];
    const maxSize = 50 * 1024 * 1024; // 50MB

    for (const file of files) {
      if (!validTypes.includes(file.type)) {
        this.showError(`Invalid file type: ${file.name}. Please select images or videos only.`);
        this.selectedFiles = [];
        return;
      }
      if (file.size > maxSize) {
        this.showError(`File too large: ${file.name}. Maximum size is 50MB.`);
        this.selectedFiles = [];
        return;
      }
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  onAspectRatioChange(): void {
    const ratio = this.aspectRatios.find(r => r.value === this.selectedAspectRatio);
    if (ratio) {
      this.previewDimensions = { width: ratio.width, height: ratio.height };
    }
  }

  getAspectRatioStyle(): any {
    return {
      'aspect-ratio': this.selectedAspectRatio.replace(':', '/'),
      'width': '100%',
      'max-width': `${this.previewDimensions.width}px`,
      'height': 'auto'
    };
  }

  getFilePreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  onSubmit(): void {
    if (this.postForm.invalid) {
      this.postForm.markAllAsTouched();
      this.showError('Please fill in all required fields correctly.');
      return;
    }

    // For edit mode, files are optional (can keep existing media)
    if (!this.isEditMode && this.selectedFiles.length === 0) {
      this.showError('Please select at least one media file.');
      return;
    }

    this.isSubmitting = true;
    const formValue = this.postForm.value;

    // Combine date and time
    const scheduledDate = new Date(formValue.scheduled_date);
    const [hours, minutes] = formValue.scheduled_time.split(':');
    scheduledDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    const formData = new FormData();
    formData.append('title', formValue.title);
    formData.append('description', formValue.description);
    formData.append('project', formValue.project.toString());
    formData.append('scheduled_time', scheduledDate.toISOString());

    // Add the first selected file as media (if any)
    if (this.selectedFiles.length > 0) {
      formData.append('media', this.selectedFiles[0], this.selectedFiles[0].name);
    }

    if (this.isEditMode && this.postId) {
      // Update existing post
      this.postService.updatePost(this.postId, formData).subscribe({
        next: (response) => {
          this.showSuccess('Post updated successfully!');
          this.router.navigate(['/creators/post-list']);
        },
        error: (error) => {
          console.error('Error updating post:', error);
          this.showError('Failed to update post. Please try again.');
          this.isSubmitting = false;
        }
      });
    } else {
      // Create new post
      this.postService.createPostWithFiles(formData).subscribe({
        next: (response) => {
          this.showSuccess('Post created successfully!');
          this.router.navigate(['/creators/post-list']);
        },
        error: (error) => {
          console.error('Error creating post:', error);
          this.showError('Failed to create post. Please try again.');
          this.isSubmitting = false;
        }
      });
    }
  }

  // Utility methods
  getFileType(file: File): string {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type.startsWith('video/')) return 'video';
    return 'file';
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

}
