<h2 mat-dialog-title>{{ data.actionType === 'reject' ? 'Reject Post' : 'Request Changes for Post' }}</h2>
<mat-dialog-content>
  <p>Are you sure you want to {{ data.actionType === 'reject' ? 'reject' : 'request changes for' }} this post?</p>
  <mat-form-field appearance="fill" class="full-width-input">
    <mat-label>Comments (Optional)</mat-label>
    <textarea matInput cdkTextareaAutosize #autosize="cdkTextareaAutosize" cdkAutosizeMinRows="3" cdkAutosizeMaxRows="10" [(ngModel)]="reviewComments"></textarea>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="warn" (click)="onSubmit()" cdkFocusInitial>{{ data.actionType === 'reject' ? 'Reject' : 'Request Changes' }}</button>
</mat-dialog-actions> 