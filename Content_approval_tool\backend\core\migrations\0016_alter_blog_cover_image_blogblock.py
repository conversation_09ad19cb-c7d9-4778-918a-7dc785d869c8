# Generated by Django 5.2 on 2025-07-08 06:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0015_blog_media_height_blog_media_width_post_media_height_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="blog",
            name="cover_image",
            field=models.ImageField(blank=True, null=True, upload_to="blogs/"),
        ),
        migrations.CreateModel(
            name="BlogBlock",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "block_type",
                    models.CharField(
                        choices=[
                            ("heading", "Heading"),
                            ("paragraph", "Paragraph"),
                            ("image", "Image"),
                            ("quote", "Quote"),
                            ("code", "Code"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        blank=True, help_text="Text content for text-based blocks"
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        help_text="Image for image blocks",
                        null=True,
                        upload_to="blogs/blocks/",
                    ),
                ),
                (
                    "order",
                    models.PositiveIntegerField(
                        default=0, help_text="Order of the block in the blog"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "blog",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="blocks",
                        to="core.blog",
                    ),
                ),
            ],
            options={
                "ordering": ["order"],
            },
        ),
    ]
