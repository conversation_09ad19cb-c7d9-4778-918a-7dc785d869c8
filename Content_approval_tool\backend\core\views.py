from django.shortcuts import render
from rest_framework import viewsets, permissions
from .models import User, Company, Project, Post, Blog
from .serializers import UserSerializer, CompanySerializer, ProjectSerializer, PostSerializer, BlogSerializer, BlogReviewSerializer, PostReviewSerializer
from django.http import JsonResponse
from django.contrib.auth import authenticate
from rest_framework.decorators import api_view,permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import CustomTokenObtainPairSerializer
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.views import exception_handler
from .serializers import UserSerializer
from django.db import models
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import PermissionDenied
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.utils import timezone
from datetime import datetime
from PIL import Image
from django.core.files.images import get_image_dimensions




# ViewSets
class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer


class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer


class ProjectViewSet(viewsets.ModelViewSet):
    queryset = Project.objects.all()
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter projects based on user role"""
        user = self.request.user
        if user.role == 'super_admin':
            return Project.objects.all()
        elif user.role == 'company_admin':
            return Project.objects.filter(company_admin=user)
        elif user.role == 'creator':
            return Project.objects.filter(creators=user)
        return Project.objects.none()

    def perform_create(self, serializer):
        """Only super_admin and company_admin can create projects"""
        user = self.request.user
        if user.role not in ['super_admin', 'company_admin']:
            raise PermissionDenied("You don't have permission to create projects.")
        serializer.save()


class PostViewSet(viewsets.ModelViewSet):
    queryset = Post.objects.all()
    serializer_class = PostSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.role == 'super_admin':
            return Post.objects.all()
        elif user.role == 'company_admin':
            return Post.objects.filter(project__company=user.company)
        elif user.role == 'creator':
            return Post.objects.filter(creator=user)
        return Post.objects.none()

    def get_serializer_class(self):
        if self.action in ['approve_post', 'reject_post', 'request_changes_post']:
            return PostReviewSerializer
        return PostSerializer

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def approve_post(self, request, pk=None):
        post = self.get_object()
        if request.user.role != 'company_admin' or request.user.company != post.project.company:
            raise PermissionDenied("You do not have permission to approve this post.")
        serializer = self.get_serializer(post, data={'status': 'approved'}, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def reject_post(self, request, pk=None):
        post = self.get_object()
        if request.user.role != 'company_admin' or request.user.company != post.project.company:
            raise PermissionDenied("You do not have permission to reject this post.")
        serializer = self.get_serializer(post, data={'status': 'rejected'}, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def request_changes_post(self, request, pk=None):
        post = self.get_object()
        if request.user.role != 'company_admin' or request.user.company != post.project.company:
            raise PermissionDenied("You do not have permission to request changes for this post.")
        serializer = self.get_serializer(post, data={'status': 'rework'}, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

class BlogViewSet(viewsets.ModelViewSet):
    queryset = Blog.objects.all()
    serializer_class = BlogSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.role == 'super_admin':
            return Blog.objects.all()
        elif user.role == 'company_admin':
            # Company admins can see all blogs from creators in their company
            return Blog.objects.filter(author__company=user.company)
        elif user.role == 'creator':
            # Creators can only see their own blogs
            return Blog.objects.filter(author=user)
        return Blog.objects.none()

    def get_serializer_class(self):
        if self.action in ['approve_blog', 'reject_blog', 'changes_requested']:
            return BlogReviewSerializer
        return BlogSerializer

    def perform_create(self, serializer):
        # The serializer handles setting the author and initial status
        serializer.save()

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def submit_blog(self, request, pk=None):
        blog = self.get_object()
        if request.user != blog.author:
            raise PermissionDenied("You do not have permission to submit this blog.")
        if blog.status == 'submitted':
            return Response({'detail': 'Blog already submitted for review.'}, status=status.HTTP_400_BAD_REQUEST)
        blog.status = 'submitted'
        blog.submitted_at = timezone.now() # Set submitted_at here
        blog.save()
        return Response({'status': 'blog submitted for review'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def approve_blog(self, request, pk=None):
        blog = self.get_object()
        if request.user.role != 'company_admin' or request.user.company != blog.author.company:
            raise PermissionDenied("You do not have permission to approve this blog.")
        serializer = self.get_serializer(blog, data={'status': 'approved', 'reviewed_by': request.user.id, 'reviewed_at': timezone.now()}, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def reject_blog(self, request, pk=None):
        blog = self.get_object()
        if request.user.role != 'company_admin' or request.user.company != blog.author.company:
            raise PermissionDenied("You do not have permission to reject this blog.")
        serializer = self.get_serializer(blog, data={'status': 'rejected', 'reviewed_by': request.user.id, 'reviewed_at': timezone.now()}, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def changes_requested(self, request, pk=None):
        blog = self.get_object()
        if request.user.role != 'company_admin' or request.user.company != blog.author.company:
            raise PermissionDenied("You do not have permission to request changes for this blog.")
        review_comment = request.data.get('review_comment', None)
        serializer = self.get_serializer(blog, data={'status': 'changes_requested', 'review_comments': review_comment, 'reviewed_by': request.user.id, 'reviewed_at': timezone.now()}, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def request_rework(self, request, pk=None):
        blog = self.get_object()
        if request.user.role != 'company_admin' or request.user.company != blog.author.company:
            raise PermissionDenied("You do not have permission to request rework for this blog.")
        review_comment = request.data.get('review_comment', None)
        serializer = self.get_serializer(blog, data={'status': 'rework', 'review_comments': review_comment, 'reviewed_by': request.user.id, 'reviewed_at': timezone.now()}, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        """Override to add better logging and error handling"""
        try:
            response = super().post(request, *args, **kwargs)
            if response.status_code == 200:
                # Log successful authentication
                username = request.data.get('username', 'unknown')
                print(f"✅ Successful token generation for user: {username}")
            return response
        except Exception as e:
            # Log authentication failures with more detail
            username = request.data.get('username', 'unknown')
            print(f"❌ Token generation failed for user: {username}, Error: {str(e)}")
            return Response(
                {'detail': 'Authentication failed. Please check your credentials.'},
                status=401
            )

# JWT login endpoint
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_current_user(request):
    user = request.user
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'role': user.role,
        'company': {
            'id': user.company.id,
            'name': user.company.name
        } if user.company else None,
        'is_active': user.is_active,
        'date_joined': user.date_joined.isoformat()
    })

@api_view(['POST'])
def login_view(request):
    username = request.data.get('username')
    password = request.data.get('password')
    user = authenticate(username=username, password=password)

    if user:
        refresh = RefreshToken.for_user(user)
        return JsonResponse({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'company': {
                    'id': user.company.id,
                    'name': user.company.name
                } if user.company else None,
                'is_active': user.is_active,
                'date_joined': user.date_joined.isoformat()
            }
        })
    return JsonResponse({'error': 'Invalid credentials'}, status=401)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_posts(request):
    user = request.user
    if user.role == 'creator':
        posts = Post.objects.filter(creator=user)
    elif user.role == 'company_admin':
        # Only show posts from projects assigned to this company admin
        posts = Post.objects.filter(project__company_admin=user)
    else:
        posts = Post.objects.all()

    serializer = PostSerializer(posts, many=True, context={'request': request})
    return Response(serializer.data)

class SuperAdminViewSet(viewsets.ViewSet):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def _check_super_admin_permission(self, request):
        """Check if user is super admin"""
        if request.user.role != 'super_admin':
            return Response({'error': 'Access denied. Super admin role required.'}, status=403)
        return None

    def _check_permission_matrix(self, request, action):
        """Enforce permission matrix rules"""
        user_role = request.user.role

        # Permission matrix implementation
        permissions = {
            'create_company': ['super_admin'],
            'create_project': ['super_admin', 'company_admin'],
            'set_project_title_deadline': ['super_admin', 'company_admin'],
            'review_post': ['company_admin'],
            'post_creation_submit': ['creator'],
            'assign_creator': ['super_admin', 'company_admin']
        }

        if action in permissions and user_role not in permissions[action]:
            return Response({
                'error': f'Access denied. {action} requires one of: {", ".join(permissions[action])}'
            }, status=403)
        return None

    # List companies with detailed admin and creator information
    @action(detail=False, methods=['get'])
    def list_companies(self, request):
        """Get all companies with detailed admin and creator information"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        companies = Company.objects.all().prefetch_related('users', 'projects')
        companies_data = []

        for company in companies:
            # Get admins and creators for this company
            admins = company.users.filter(role='company_admin')
            creators = company.users.filter(role='creator')
            projects = company.projects.all()

            companies_data.append({
                'id': company.id,
                'name': company.name,
                'logo': company.logo.url if company.logo else None,
                'is_active': True,  # Default to active for now
                'admin_count': admins.count(),
                'creator_count': creators.count(),
                'project_count': projects.count(),
                'admins': [
                    {
                        'id': admin.id,
                        'username': admin.username,
                        'first_name': admin.first_name,
                        'last_name': admin.last_name,
                        'email': admin.email,
                        'full_name': f"{admin.first_name} {admin.last_name}".strip() or admin.username,
                        'date_joined': admin.date_joined.isoformat()
                    } for admin in admins
                ],
                'creators': [
                    {
                        'id': creator.id,
                        'username': creator.username,
                        'first_name': creator.first_name,
                        'last_name': creator.last_name,
                        'email': creator.email,
                        'full_name': f"{creator.first_name} {creator.last_name}".strip() or creator.username,
                        'assigned_projects_count': creator.assigned_projects.filter(company=company).count(),
                        'date_joined': creator.date_joined.isoformat()
                    } for creator in creators
                ],
                'projects': [
                    {
                        'id': project.id,
                        'name': project.name,
                        'title': project.title,
                        'deadline': project.deadline.isoformat() if project.deadline else None,
                        'creators_count': project.creators.count()
                    } for project in projects
                ],
                'status': 'active' if admins.count() > 0 or creators.count() > 0 else 'inactive'
            })

        return Response(companies_data)
    
    # Add a new company
    @action(detail=False, methods=['post'])
    def add_company(self, request):
        # Check permission matrix
        permission_error = self._check_permission_matrix(request, 'create_company')
        if permission_error:
            return permission_error

        serializer = CompanySerializer(data=request.data)
        if serializer.is_valid():
            company = serializer.save()
            return Response({"message": "Company created successfully", "company": serializer.data})
        return Response(serializer.errors, status=400)
    
    # Add a project to a company
    @action(detail=True, methods=['post'])
    def add_project(self, request, pk=None):
        # Check permission matrix
        permission_error = self._check_permission_matrix(request, 'create_project')
        if permission_error:
            return permission_error

        try:
            company = Company.objects.get(id=pk)
        except Company.DoesNotExist:
            return Response({"error": "Company not found."}, status=404)

        project_data = request.data
        project_data['company'] = company.id
        serializer = ProjectSerializer(data=project_data)

        if serializer.is_valid():
            serializer.save()

            # Fetch and return updated project list
            projects = Project.objects.filter(company=company)
            project_list_serializer = ProjectSerializer(projects, many=True)

            return Response({
                "message": "Project added successfully",
                "projects": project_list_serializer.data
            })
        return Response(serializer.errors, status=400)

    # Assign a content creator to a project
    @action(detail=True, methods=['post'], url_path='assign_creator')
    def assign_creator(self, request, pk=None):
        # Check permission matrix
        permission_error = self._check_permission_matrix(request, 'assign_creator')
        if permission_error:
            return permission_error

        try:
            project_id = pk
            creator_id = request.data.get('creator_id')

            project = Project.objects.get(id=project_id)
            creator = User.objects.get(id=creator_id)
            project.creators.add(creator)

            return Response({"message": "Creator assigned successfully."}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    # ✅ List projects under a company
    @action(detail=False, methods=['get'], url_path='list_projects/(?P<company_id>[^/.]+)')
    def list_projects(self, request, company_id=None):
        try:
            company = Company.objects.get(id=company_id)
            projects = Project.objects.filter(company=company).prefetch_related('creators')

            project_data = []
            for project in projects:
                creators = project.creators.all()
                creators_data = [{'id': c.id, 'username': c.username} for c in creators]
                project_data.append({
                    'id': project.id,
                    'name': project.name,
                    'title': project.title,
                    'description': project.description,
                    'deadline': project.deadline.isoformat() if project.deadline else None,
                    'company': company.id,
                    'company_admin_detail': {
                        'id': project.company_admin.id,
                        'username': project.company_admin.username,
                        'first_name': project.company_admin.first_name,
                        'last_name': project.company_admin.last_name,
                        'email': project.company_admin.email
                    } if project.company_admin else None,
                    'creators': creators_data,
                    'created_at': project.created_at.isoformat() if project.created_at else None
                })

            return Response(project_data)
        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)

    # ✅ Get dashboard statistics for Super Admin
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get comprehensive dashboard statistics for Super Admin"""
        # Company statistics
        total_companies = Company.objects.count()
        active_companies = Company.objects.filter(users__isnull=False).distinct().count()

        # Admin statistics
        total_admins = User.objects.filter(role='company_admin').count()
        active_admins = User.objects.filter(role='company_admin', company__isnull=False).count()

        # Creator statistics
        total_creators = User.objects.filter(role='creator').count()
        assigned_creators = User.objects.filter(role='creator', assigned_projects__isnull=False).distinct().count()

        # Content statistics
        total_posts = Post.objects.count()
        pending_reviews = Post.objects.filter(status='submitted').count()

        return Response({
            'companies': {
                'total': total_companies,
                'active': active_companies
            },
            'admins': {
                'total': total_admins,
                'active': active_admins
            },
            'creators': {
                'total': total_creators,
                'assigned': assigned_creators
            },
            'content': {
                'total_posts': total_posts,
                'pending_reviews': pending_reviews
            }
        })

    # ✅ List all users by role with improved filtering
    @action(detail=False, methods=['get'])
    def list_users(self, request):
        """List users filtered by role with eligibility filtering for admin assignment"""
        role = request.query_params.get('role', None)
        eligible_for_admin = request.query_params.get('eligible_for_admin', 'false').lower() == 'true'

        if role:
            users = User.objects.filter(role=role)
        else:
            users = User.objects.all()

        # Filter users eligible for company admin assignment
        if eligible_for_admin:
            users = users.filter(
                models.Q(role='creator') |  # Creators can be promoted to admin
                models.Q(role='super_admin') |  # Super admins can be assigned as company admin
                models.Q(role='company_admin', company__isnull=True)  # Unassigned company admins
            ).exclude(
                role='company_admin', company__isnull=False  # Exclude already assigned company admins
            )

        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'company': {
                    'id': user.company.id,
                    'name': user.company.name
                } if user.company else None,
                'is_active': user.is_active,
                'date_joined': user.date_joined.isoformat(),
                'eligible_for_admin': user.role in ['creator', 'super_admin'] or (user.role == 'company_admin' and not user.company)
            })

        return Response(users_data)

    # ✅ Assign Company Admin to a company
    @action(detail=False, methods=['post'])
    def assign_admin(self, request):
        """Assign a user as Company Admin to a specific company"""
        user_id = request.data.get('user_id')
        company_id = request.data.get('company_id')

        try:
            user = User.objects.get(id=user_id)
            company = Company.objects.get(id=company_id)

            # Update user role and company
            user.role = 'company_admin'
            user.company = company
            user.save()

            return Response({
                'message': f'User {user.username} assigned as Company Admin to {company.name}',
                'user_id': user.id,
                'company_id': company.id
            })

        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=404)
        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

    # ✅ Assign Content Creator to a company
    @action(detail=False, methods=['post'])
    def assign_creator_to_company(self, request):
        """Assign a creator to a company by linking them to company projects"""
        creator_id = request.data.get('creator_id')
        company_id = request.data.get('company_id')

        try:
            creator = User.objects.get(id=creator_id, role='creator')
            company = Company.objects.get(id=company_id)

            # Update creator's company association
            creator.company = company
            creator.save()

            return Response({
                'message': f'Creator {creator.username} assigned to {company.name}',
                'creator_id': creator.id,
                'company_id': company.id
            })

        except User.DoesNotExist:
            return Response({'error': 'Creator not found'}, status=404)
        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

    # ✅ Get recent activity logs
    @action(detail=False, methods=['get'])
    def activity_logs(self, request):
        """Get recent activity logs for Super Admin dashboard"""
        # For now, we'll create mock activity data based on recent database changes
        # In a production system, you'd want a proper activity logging system

        activities = []

        # Recent company admins
        recent_admins = User.objects.filter(
            role='company_admin',
            company__isnull=False
        ).order_by('-date_joined')[:5]

        for admin in recent_admins:
            activities.append({
                'id': f'admin_{admin.id}',
                'type': 'admin_assignment',
                'message': f'Admin {admin.username} assigned to {admin.company.name}',
                'timestamp': admin.date_joined.isoformat(),
                'user': admin.username,
                'company': admin.company.name if admin.company else None
            })

        # Recent creator assignments
        recent_creators = User.objects.filter(
            role='creator',
            company__isnull=False
        ).order_by('-date_joined')[:5]

        for creator in recent_creators:
            activities.append({
                'id': f'creator_{creator.id}',
                'type': 'creator_assignment',
                'message': f'Creator {creator.username} added to {creator.company.name}',
                'timestamp': creator.date_joined.isoformat(),
                'user': creator.username,
                'company': creator.company.name if creator.company else None
            })

        # Recent companies
        recent_companies = Company.objects.order_by('-id')[:3]
        for company in recent_companies:
            # Use a proper timestamp - for new companies without created_at, use current time
            import datetime
            timestamp = datetime.datetime.now().isoformat()
            activities.append({
                'id': f'company_{company.id}',
                'type': 'company_creation',
                'message': f'Company {company.name} created',
                'timestamp': timestamp,
                'company': company.name
            })

        # Sort by timestamp (most recent first)
        activities.sort(key=lambda x: x['timestamp'], reverse=True)

        return Response(activities[:10])  # Return top 10 recent activities

    # ✅ Create Project with Title and Deadline
    @action(detail=False, methods=['post'])
    def create_project(self, request):
        """Create a new project with title, deadline, and assignments"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        permission_error = self._check_permission_matrix(request, 'create_project')
        if permission_error:
            return permission_error

        try:
            data = request.data

            # Required fields validation
            required_fields = ['name', 'title', 'company_id']
            for field in required_fields:
                if not data.get(field):
                    return Response({'error': f'{field} is required'}, status=400)

            # Deadline is recommended but not strictly required
            if not data.get('deadline'):
                print("Warning: Project created without deadline")

            # Validate company exists
            try:
                company = Company.objects.get(id=data['company_id'])
            except Company.DoesNotExist:
                return Response({'error': 'Company not found'}, status=404)

            # Handle deadline formatting
            deadline_value = data.get('deadline')
            parsed_deadline = None
            if deadline_value:
                if isinstance(deadline_value, str):
                    from datetime import datetime
                    try:
                        # Try parsing ISO format first
                        parsed_deadline = datetime.fromisoformat(deadline_value.replace('Z', '+00:00'))
                    except ValueError:
                        try:
                            # Try parsing other common formats
                            parsed_deadline = datetime.strptime(deadline_value, '%Y-%m-%d')
                        except ValueError:
                            print(f"Could not parse deadline: {deadline_value}")
                            parsed_deadline = None
                else:
                    parsed_deadline = deadline_value

            # Create project
            project = Project.objects.create(
                name=data['name'],
                title=data['title'],
                description=data.get('description', ''),
                deadline=parsed_deadline,
                company=company,
                created_by=request.user
            )

            # Assign creators if provided
            creator_ids = data.get('creator_ids', [])
            if creator_ids:
                creators = User.objects.filter(id__in=creator_ids, role='creator')
                project.creators.set(creators)

            # Assign company admin if provided
            company_admin_id = data.get('company_admin_id')
            if company_admin_id:
                try:
                    admin = User.objects.get(id=company_admin_id, role='company_admin')
                    project.company_admin = admin
                    project.save()
                except User.DoesNotExist:
                    pass  # Continue without error if admin not found

            return Response({
                'message': 'Project created successfully',
                'project': {
                    'id': project.id,
                    'name': project.name,
                    'title': project.title,
                    'description': project.description,
                    'deadline': project.deadline.isoformat() if project.deadline else None,
                    'company': company.name,
                    'creators': [{'id': c.id, 'username': c.username} for c in project.creators.all()]
                }
            }, status=201)

        except Exception as e:
            return Response({'error': str(e)}, status=500)

    # ✅ Update Project
    @action(detail=False, methods=['put'], url_path='update_project/(?P<project_id>[^/.]+)')
    def update_project(self, request, project_id=None):
        """Update an existing project"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            print(f"Updating project {project_id} with data: {request.data}")
            project = Project.objects.get(id=project_id)
            data = request.data

            # Update project fields
            if 'name' in data:
                project.name = data['name']
            if 'title' in data:
                project.title = data['title']
            if 'description' in data:
                project.description = data['description']
            if 'deadline' in data:
                deadline_value = data['deadline']
                if deadline_value:
                    # Handle both string and date formats
                    if isinstance(deadline_value, str):
                        from datetime import datetime
                        try:
                            # Try parsing ISO format first
                            project.deadline = datetime.fromisoformat(deadline_value.replace('Z', '+00:00'))
                        except ValueError:
                            try:
                                # Try parsing other common formats
                                project.deadline = datetime.strptime(deadline_value, '%Y-%m-%d')
                            except ValueError:
                                print(f"Could not parse deadline: {deadline_value}")
                                project.deadline = None
                    else:
                        project.deadline = deadline_value
                else:
                    project.deadline = None

            project.save()
            print(f"Project saved successfully: {project.name}")

            # Update creators if provided
            if 'creator_ids' in data:
                project.creators.set(data['creator_ids'])
                print(f"Updated creators: {data['creator_ids']}")

            # Update company admin if provided
            if 'company_admin_id' in data:
                company_admin_id = data['company_admin_id']
                if company_admin_id:
                    try:
                        admin = User.objects.get(id=company_admin_id, role='company_admin')
                        project.company_admin = admin
                        project.save()
                        print(f"Updated company admin: {admin.username}")
                    except User.DoesNotExist:
                        print(f"Company admin with id {company_admin_id} not found")
                else:
                    project.company_admin = None
                    project.save()
                    print("Removed company admin assignment")

            return Response({
                'message': 'Project updated successfully',
                'project': {
                    'id': project.id,
                    'name': project.name,
                    'title': project.title,
                    'description': project.description,
                    'deadline': project.deadline.isoformat() if project.deadline else None,
                    'company': project.company.name,
                    'creators': [{'id': c.id, 'username': c.username} for c in project.creators.all()]
                }
            })

        except Project.DoesNotExist:
            print(f"Project {project_id} not found")
            return Response({'error': 'Project not found'}, status=404)
        except Exception as e:
            print(f"Error updating project: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({'error': str(e)}, status=500)

    # ✅ Get Company Settings and Projects
    @action(detail=True, methods=['get'])
    def company_settings(self, request, pk=None):
        """Get company settings including projects and assignments"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            company = Company.objects.get(id=pk)

            # Get company projects
            projects = Project.objects.filter(company=company).prefetch_related('creators')

            # Get company admin
            company_admin = User.objects.filter(company=company, role='company_admin').first()

            # Get available users for assignment
            available_users = User.objects.filter(
                models.Q(role='creator') |
                models.Q(role='company_admin') |
                models.Q(role__in=['super_admin'], company__isnull=True)
            )

            return Response({
                'company': {
                    'id': company.id,
                    'name': company.name
                },
                'admin': {
                    'id': company_admin.id,
                    'username': company_admin.username,
                    'email': company_admin.email
                } if company_admin else None,
                'projects': [
                    {
                        'id': p.id,
                        'name': p.name,
                        'title': p.title,
                        'description': p.description,
                        'deadline': p.deadline.isoformat() if p.deadline else None,
                        'company_admin_detail': {
                            'id': p.company_admin.id,
                            'username': p.company_admin.username,
                            'first_name': p.company_admin.first_name,
                            'last_name': p.company_admin.last_name,
                            'email': p.company_admin.email
                        } if p.company_admin else None,
                        'creators': [{'id': c.id, 'username': c.username} for c in p.creators.all()],
                        'created_at': p.created_at.isoformat()
                    } for p in projects
                ],
                'available_users': [
                    {
                        'id': u.id,
                        'username': u.username,
                        'role': u.role,
                        'email': u.email,
                        'first_name': u.first_name,
                        'last_name': u.last_name,
                        'company': {
                            'id': u.company.id,
                            'name': u.company.name
                        } if u.company else None
                    } for u in available_users
                ]
            })

        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)

    # ✅ Delete Project
    @action(detail=False, methods=['delete'], url_path='delete_project/(?P<project_id>[^/.]+)')
    def delete_project(self, request, project_id=None):
        """Delete a project"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            project = Project.objects.get(id=project_id)
            project_name = project.name
            project.delete()

            return Response({
                'message': f'Project "{project_name}" deleted successfully'
            })

        except Project.DoesNotExist:
            return Response({'error': 'Project not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    # ✅ Delete Company
    @action(detail=False, methods=['delete'], url_path='delete_company/(?P<company_id>[^/.]+)')
    def delete_company(self, request, company_id=None):
        """Delete a company and all its associated projects"""
        permission_error = self._check_super_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            company = Company.objects.get(id=company_id)
            company_name = company.name

            # Delete all associated projects first
            projects_count = company.projects.count()
            company.delete()

            return Response({
                'message': f'Company "{company_name}" and {projects_count} associated projects deleted successfully'
            })

        except Company.DoesNotExist:
            return Response({'error': 'Company not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)
        
class CreatorDashboardViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

    def _check_creator_permission(self, request):
        """Check if user is creator"""
        if request.user.role != 'creator':
            return Response({'error': 'Access denied. Creator role required.'}, status=403)
        return None

    @action(detail=False, methods=['get'])
    def my_projects(self, request):
        """Get projects assigned to the creator with company details and deadlines"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        user = request.user
        projects = Project.objects.filter(creators=user).select_related('company', 'created_by')
        data = []
        for project in projects:
            # Get project deadline from latest post or set a default
            latest_post = Post.objects.filter(project=project).order_by('-scheduled_date').first()
            deadline = latest_post.scheduled_date if latest_post and latest_post.scheduled_date else None

            # Use project deadline if available, otherwise use latest post deadline
            if project.deadline:
                deadline = project.deadline

            # Get the company admin who assigned this project
            company_admin = project.created_by if project.created_by and project.created_by.role == 'company_admin' else None

            # If no specific admin assigned the project, get the first company admin for this company
            if not company_admin:
                company_admin = project.company.users.filter(role='company_admin').first()

            data.append({
                'id': project.id,
                'name': project.name,
                'title': project.title or project.name,  # Use project title if available
                'description': project.description,
                'company': {
                    'id': project.company.id,
                    'name': project.company.name,
                    'logo': project.company.logo.url if project.company.logo else None
                },
                'company_name': project.company.name,  # For easy access
                'company_admin': {
                    'id': company_admin.id,
                    'username': company_admin.username,
                    'first_name': company_admin.first_name,
                    'last_name': company_admin.last_name,
                    'email': company_admin.email,
                    'full_name': f"{company_admin.first_name} {company_admin.last_name}".strip() or company_admin.username
                } if company_admin else None,
                'deadline': deadline.isoformat() if deadline else None,
                'created_at': project.created_at.isoformat() if project.created_at else None,
                'posts_count': Post.objects.filter(project=project, creator=user).count(),
                'pending_posts_count': Post.objects.filter(
                    project=project,
                    creator=user,
                    status='submitted'
                ).count()
            })
        return Response(data)

    @action(detail=False, methods=['get'])
    def my_posts(self, request):
        """Get posts created by the creator, filtered by assigned projects only"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        # Get assigned projects
        assigned_projects = Project.objects.filter(creators=request.user)
        print(f"User {request.user.username} has {assigned_projects.count()} assigned projects")

        # Get posts from assigned projects (created by this user)
        posts = Post.objects.filter(
            creator=request.user,
            project__in=assigned_projects
        ).select_related('project', 'project__company')

        print(f"Found {posts.count()} posts for user {request.user.username}")

        serializer = PostSerializer(posts, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def calendar_data(self, request):
        """Get calendar data with project assignments, posts, and blogs as events"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        user = request.user
        # Get assigned projects
        projects = Project.objects.filter(creators=user).select_related('company')

        # Get posts for assigned projects only
        posts = Post.objects.filter(
            creator=user,
            project__in=projects
        ).select_related('project', 'project__company')

        # Get blogs for assigned projects only
        blogs = Blog.objects.filter(
            author=user,
            project__in=projects
        ).select_related('project', 'project__company')

        calendar_events = []

        # Add project events (deadlines)
        for project in projects:
            latest_post = Post.objects.filter(project=project).order_by('-scheduled_date').first()
            if latest_post and latest_post.scheduled_date:
                calendar_events.append({
                    'id': f'project_{project.id}',
                    'type': 'project',
                    'title': project.name,
                    'company_name': project.company.name,
                    'date': latest_post.scheduled_date.isoformat(),
                    'deadline': latest_post.scheduled_date.isoformat(),
                    'project_id': project.id,
                    'company_id': project.company.id
                })

        # Add post events with thumbnails
        for post in posts:
            event_data = {
                'id': f'post_{post.id}',
                'type': 'post',
                'title': post.title,
                'description': post.description,
                'project_title': post.project.name,
                'company_name': post.project.company.name,
                'date': post.scheduled_date.isoformat() if post.scheduled_date else post.scheduled_time.isoformat(),
                'deadline': post.scheduled_date.isoformat() if post.scheduled_date else None,
                'status': post.status,
                'project_id': post.project.id,
                'company_id': post.project.company.id,
                'post_id': post.id
            }
            if post.media:
                request_obj = self.request if hasattr(self, 'request') else request
                event_data['thumbnail_url'] = request_obj.build_absolute_uri(post.media.url)
                if post.media.name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                    event_data['media_type'] = 'image'
                elif post.media.name.lower().endswith(('.mp4', '.webm', '.ogg', '.avi', '.mov')):
                    event_data['media_type'] = 'video'
                else:
                    event_data['media_type'] = 'file'
            calendar_events.append(event_data)

        # Add blog events
        for blog in blogs:
            event_data = {
                'id': f'blog_{blog.id}',
                'type': 'blog',
                'title': blog.title,
                'description': blog.content,
                'project_title': blog.project.name if blog.project else None,
                'company_name': blog.project.company.name if blog.project and blog.project.company else None,
                'date': blog.submitted_at.isoformat() if blog.submitted_at else None,
                'status': blog.status,
                'project_id': blog.project.id if blog.project else None,
                'company_id': blog.project.company.id if blog.project and blog.project.company else None,
                'blog_id': blog.id
            }
            if blog.cover_image:
                request_obj = self.request if hasattr(self, 'request') else request
                event_data['thumbnail_url'] = request_obj.build_absolute_uri(blog.cover_image.url)
                event_data['media_type'] = 'image'
            calendar_events.append(event_data)

        return Response({
            'events': calendar_events,
            'projects': [
                {
                    'id': p.id,
                    'name': p.name,
                    'company_name': p.company.name,
                    'company_id': p.company.id
                } for p in projects
            ]
        })

    @action(detail=False, methods=['post'])
    def upload_post(self, request):
        """Create a new post with file upload"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        try:
            # Initialize data dictionary first
            data = {}

            # Get the data from the request (don't copy to avoid file pickling issues)
            for key, value in request.data.items():
                if key != 'media':  # Handle file separately
                    data[key] = value

            # Debug logging
            print(f"Received data: {data}")
            print(f"Request user: {request.user}")
            print(f"Files: {list(request.FILES.keys())}")

            # Set the creator to the current user
            data['creator'] = request.user.id

            # Handle file upload and dimensions
            media = request.FILES.get('media')
            if media:
                data['media'] = media
                # Safely handle image dimensions
                if media.content_type.startswith('image'):
                    try:
                        width, height = get_image_dimensions(media)
                        data['media_width'] = width
                        data['media_height'] = height
                    except Exception as e:
                        print(f"Error getting image dimensions: {e}")
                        # Continue without dimensions if extraction fails

            # Handle scheduled_time format
            scheduled_time = data.get('scheduled_time')
            if scheduled_time:
                from datetime import datetime
                try:
                    # If it's a datetime-local format, convert it to proper datetime
                    if 'T' in scheduled_time:
                        if len(scheduled_time) == 16:  # YYYY-MM-DDTHH:MM
                            scheduled_time += ':00'  # Add seconds
                        # Parse and convert to datetime object
                        parsed_datetime = datetime.fromisoformat(scheduled_time)
                        data['scheduled_time'] = parsed_datetime
                        data['scheduled_date'] = parsed_datetime  # Keep as datetime, not date
                    else:
                        # If it's just a date, use current time
                        date_obj = datetime.strptime(scheduled_time, '%Y-%m-%d')
                        data['scheduled_time'] = date_obj
                        data['scheduled_date'] = date_obj  # Keep as datetime, not date
                except ValueError as e:
                    print(f"Error parsing scheduled_time: {e}")
                    # Use current datetime as fallback
                    now = datetime.now()
                    data['scheduled_time'] = now
                    data['scheduled_date'] = now

            # Validate that the project is assigned to this creator
            project_id = data.get('project')
            if not project_id:
                return Response({'error': 'Project is required.'}, status=400)

            try:
                project = Project.objects.get(id=project_id, creators=request.user)
            except Project.DoesNotExist:
                return Response({'error': 'You are not assigned to this project.'}, status=403)

            # Create the post
            serializer = PostSerializer(data=data, context={'request': request})
            if serializer.is_valid():
                post = serializer.save()
                return Response(PostSerializer(post, context={'request': request}).data, status=201)
            else:
                print(f"Serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=400)

        except Exception as e:
            print(f"Exception in upload_post: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({'error': str(e)}, status=500)

    @action(detail=True, methods=['patch'])
    def update_status(self, request, pk=None):
        """Update post status (for creators to submit for review)"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(id=pk, creator=request.user)
            new_status = request.data.get('status')

            # Validate status transitions for creators
            allowed_transitions = {
                'draft': ['submitted'],
                'rejected': ['submitted'],
                'rework': ['submitted']
            }

            if post.status not in allowed_transitions or new_status not in allowed_transitions[post.status]:
                return Response({'error': f'Cannot change status from {post.status} to {new_status}'}, status=400)

            post.status = new_status
            post.save()

            return Response({
                'message': 'Post status updated successfully',
                'post': PostSerializer(post, context={'request': request}).data
            })

        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not accessible'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=True, methods=['put', 'patch'])
    def update_post(self, request, pk=None):
        """Update an existing post (for creators to edit their posts)"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(id=pk, creator=request.user)

            # Only allow editing posts in draft or rework status
            if post.status not in ['draft', 'rework']:
                return Response({'error': f'Cannot edit post with status: {post.status}. Only draft and rework posts can be edited.'}, status=400)

            # Get the data from the request
            data = {}
            for key, value in request.data.items():
                if key != 'media':  # Handle file separately
                    data[key] = value

            # Debug logging
            print(f"Updating post {pk} with data: {data}")
            print(f"Request user: {request.user}")
            print(f"Files: {list(request.FILES.keys())}")

            # Handle file upload if provided
            if 'media' in request.FILES:
                data['media'] = request.FILES['media']

            # Don't allow changing creator or project
            data.pop('creator', None)
            data.pop('project', None)

            # Update the post
            serializer = PostSerializer(post, data=data, partial=True, context={'request': request})
            if serializer.is_valid():
                updated_post = serializer.save()
                return Response(PostSerializer(updated_post, context={'request': request}).data)
            else:
                print(f"Serializer errors: {serializer.errors}")
                return Response(serializer.errors, status=400)

        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not accessible'}, status=404)
        except Exception as e:
            print(f"Exception in update_post: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['get'])
    def my_blogs(self, request):
        """Get blogs created by the creator (filtered by assigned projects)"""
        permission_error = self._check_creator_permission(request)
        if permission_error:
            return permission_error
        assigned_projects = Project.objects.filter(creators=request.user)
        blogs = Blog.objects.filter(author=request.user, project__in=assigned_projects)
        serializer = BlogSerializer(blogs, many=True, context={'request': request})
        return Response(serializer.data)


class CompanyAdminViewSet(viewsets.ViewSet):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def _check_company_admin_permission(self, request):
        """Check if user is company admin and has access to company data"""
        if request.user.role != 'company_admin':
            return Response({'error': 'Access denied. Company admin role required.'}, status=403)
        if not request.user.company:
            return Response({'error': 'No company associated with this admin.'}, status=400)
        return None

    @action(detail=False, methods=['get'])
    def assigned_projects(self, request):
        """Get projects assigned to the current company admin"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            # Get projects where this admin is specifically assigned
            projects = Project.objects.filter(
                company_admin=request.user
            ).prefetch_related('creators', 'posts').distinct()

            projects_data = []
            for project in projects:
                projects_data.append({
                    'id': project.id,
                    'name': project.name,
                    'title': project.title,
                    'description': project.description,
                    'deadline': project.deadline.isoformat() if project.deadline else None,
                    'company': {
                        'id': project.company.id,
                        'name': project.company.name
                    },
                    'company_admin': {
                        'id': project.company_admin.id,
                        'username': project.company_admin.username,
                        'full_name': f"{project.company_admin.first_name} {project.company_admin.last_name}".strip() or project.company_admin.username
                    } if project.company_admin else None,
                    'creators': [
                        {
                            'id': creator.id,
                            'username': creator.username,
                            'full_name': f"{creator.first_name} {creator.last_name}".strip() or creator.username,
                            'email': creator.email
                        } for creator in project.creators.all()
                    ],
                    'posts_count': project.posts.count(),
                    'pending_posts': project.posts.filter(status='submitted').count(),
                    'created_at': project.created_at.isoformat() if project.created_at else None
                })

            return Response(projects_data)

        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=True, methods=['post'])
    def assign_creators(self, request, pk=None):
        """Assign creators to a project (company admin only)"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            project = Project.objects.get(
                id=pk,
                company=request.user.company
            )

            creator_ids = request.data.get('creator_ids', [])

            # Validate that all creators belong to the same company
            creators = User.objects.filter(
                id__in=creator_ids,
                role='creator',
                company=request.user.company
            )

            if len(creators) != len(creator_ids):
                return Response({'error': 'Some creators not found or not in your company'}, status=400)

            # Clear existing assignments and add new ones
            project.creators.set(creators)

            return Response({
                'message': f'Successfully assigned {len(creators)} creators to project {project.title}',
                'project_id': project.id,
                'assigned_creators': [
                    {
                        'id': creator.id,
                        'username': creator.username,
                        'full_name': f"{creator.first_name} {creator.last_name}".strip() or creator.username
                    } for creator in creators
                ]
            })

        except Project.DoesNotExist:
            return Response({'error': 'Project not found or not accessible'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['get'])
    def available_creators(self, request):
        """Get creators available for assignment in the company"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            creators = User.objects.filter(
                role='creator',
                company=request.user.company
            )

            creators_data = [
                {
                    'id': creator.id,
                    'username': creator.username,
                    'full_name': f"{creator.first_name} {creator.last_name}".strip() or creator.username,
                    'email': creator.email,
                    'assigned_projects_count': creator.assigned_projects.filter(company=request.user.company).count(),
                    'total_posts': Post.objects.filter(creator=creator, project__company=request.user.company).count(),
                    'pending_posts': Post.objects.filter(creator=creator, project__company=request.user.company, status='submitted').count()
                } for creator in creators
            ]

            return Response(creators_data)

        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get dashboard statistics for company admin"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        # Get projects specifically assigned to this company admin
        admin_projects = Project.objects.filter(company_admin=request.user)

        # Project statistics - only for this admin's projects
        total_projects = admin_projects.count()
        active_projects = admin_projects.filter(posts__status__in=['draft', 'submitted', 'scheduled']).distinct().count()
        completed_projects = admin_projects.filter(posts__status='posted').distinct().count()

        # Content statistics - only from creators assigned to this admin's projects
        assigned_creators = User.objects.filter(
            role='creator',
            assigned_projects__in=admin_projects
        ).distinct()

        # Get posts from this admin's projects only
        pending_reviews = Post.objects.filter(
            project__in=admin_projects,
            creator__in=assigned_creators,
            status='submitted'
        ).count()

        total_posts = Post.objects.filter(
            project__in=admin_projects
        ).count()

        approved_posts = Post.objects.filter(
            project__in=admin_projects,
            status='posted'
        ).count()

        rejected_posts = Post.objects.filter(
            project__in=admin_projects,
            status='rejected'
        ).count()

        # Creator statistics - count distinct creators assigned to this company's projects
        total_creators = assigned_creators.count()
        active_creators = assigned_creators.filter(
            assigned_projects__posts__status__in=['draft', 'submitted', 'scheduled']
        ).distinct().count()

        return Response({
            'company_name': request.user.company.name if request.user.company else 'No Company',
            'admin_name': request.user.get_full_name() or request.user.username,
            'projects': {
                'total': total_projects,
                'active': active_projects,
                'completed': completed_projects
            },
            'content': {
                'total_posts': total_posts,
                'pending_reviews': pending_reviews,
                'approved': approved_posts,
                'rejected': rejected_posts
            },
            'creators': {
                'total': total_creators,
                'active': active_creators
            }
        })

    @action(detail=False, methods=['get'])
    def recent_content(self, request):
        """Get recent content submissions (posts and blogs) from creators assigned to this company admin's projects"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        admin_projects = Project.objects.filter(company_admin=request.user)
        assigned_creators = User.objects.filter(
            role='creator',
            assigned_projects__in=admin_projects
        ).distinct()

        # Get recent posts
        recent_posts = Post.objects.filter(
            project__in=admin_projects
        ).select_related('creator', 'project').order_by('-created_at')[:10] # Order by created_at for posts
        post_data = [
            dict(PostSerializer(post, context={'request': request}).data, type='post', submitted_at=post.created_at if post.created_at else timezone.make_aware(datetime.min)) # Ensure datetime for posts
            for post in recent_posts
        ]

        # Get recent blogs
        recent_blogs = Blog.objects.filter(
            project__in=admin_projects
        ).select_related('author', 'project').order_by('-submitted_at')[:10]
        blog_data = [
            dict(BlogSerializer(blog, context={'request': request}).data, type='blog', submitted_at=blog.submitted_at if blog.submitted_at else timezone.make_aware(datetime.min)) # Ensure datetime for blogs
            for blog in recent_blogs
        ]

        # Merge and sort by submitted_at descending
        combined = post_data + blog_data
        # Sort by submitted_at (for blogs) or created_at (for posts) descending, handling potential None values
        combined.sort(
            key=lambda x: x.get('submitted_at') or x.get('created_at'), # The values here should now always be datetime objects
            reverse=True
        )
        return Response(combined)

    @action(detail=False, methods=['get'])
    def pending_reviews(self, request):
        """Get content (posts and blogs) pending approval from creators assigned to this company admin's projects"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        admin_projects = Project.objects.filter(company_admin=request.user)
        assigned_creators = User.objects.filter(
            role='creator',
            assigned_projects__in=admin_projects
        ).distinct()

        # Get pending posts
        pending_posts = Post.objects.filter(
            project__in=admin_projects,
            creator__in=assigned_creators,
            status='submitted'
        ).select_related('creator', 'project').order_by('-created_at') # Order by created_at for posts
        post_data = [
            dict(PostSerializer(post, context={'request': request}).data, type='post', submitted_at=post.created_at if post.created_at else timezone.make_aware(datetime.min)) # Ensure datetime for posts
            for post in pending_posts
        ]

        # Get pending blogs
        pending_blogs = Blog.objects.filter(
            project__in=admin_projects,
            status='submitted'
        ).select_related('author', 'project').order_by('-submitted_at')
        blog_data = [
            dict(BlogSerializer(blog, context={'request': request}).data, type='blog', submitted_at=blog.submitted_at if blog.submitted_at else timezone.make_aware(datetime.min)) # Ensure datetime for blogs
            for blog in pending_blogs
        ]

        # Merge and sort by submitted_at descending
        combined = post_data + blog_data
        # Sort by submitted_at (for blogs) or created_at (for posts) descending, handling potential None values
        combined.sort(
            key=lambda x: x.get('submitted_at') or x.get('created_at'), # The values here should now always be datetime objects
            reverse=True
        )
        return Response(combined)

    @action(detail=False, methods=['get'])
    def debug_relationships(self, request):
        """Debug endpoint to check data relationships"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        company = request.user.company

        # Get all data for debugging
        all_posts = Post.objects.all().select_related('creator', 'project', 'project__company')
        all_projects = Project.objects.all().select_related('company').prefetch_related('creators')
        all_users = User.objects.all().select_related('company')

        debug_data = {
            'company_admin': {
                'username': request.user.username,
                'company': company.name if company else 'No Company',
                'company_id': company.id if company else None
            },
            'posts': [],
            'projects': [],
            'users': []
        }

        for post in all_posts:
            debug_data['posts'].append({
                'id': post.id,
                'title': post.title,
                'status': post.status,
                'creator': post.creator.username,
                'creator_company': post.creator.company.name if post.creator.company else 'No Company',
                'project': post.project.name,
                'project_company': post.project.company.name,
                'creator_assigned_to_project': post.project.creators.filter(id=post.creator.id).exists()
            })

        for project in all_projects:
            debug_data['projects'].append({
                'id': project.id,
                'name': project.name,
                'company': project.company.name,
                'assigned_creators': [c.username for c in project.creators.all()]
            })

        for user in all_users:
            if user.role in ['creator', 'company_admin']:
                debug_data['users'].append({
                    'username': user.username,
                    'role': user.role,
                    'company': user.company.name if user.company else 'No Company',
                    'assigned_projects': [p.name for p in user.assigned_projects.all()] if user.role == 'creator' else []
                })

        return Response(debug_data)

    @action(detail=False, methods=['get'])
    def test_pending(self, request):
        """Simple test endpoint to check pending posts"""
        try:
            # Get all submitted posts regardless of filtering
            all_submitted = Post.objects.filter(status='submitted').select_related('creator', 'project', 'project__company')

            result = {
                'total_submitted_posts': all_submitted.count(),
                'current_user': {
                    'username': request.user.username,
                    'role': request.user.role,
                    'company': request.user.company.name if request.user.company else 'No Company'
                },
                'submitted_posts': []
            }

            for post in all_submitted:
                result['submitted_posts'].append({
                    'id': post.id,
                    'title': post.title,
                    'creator': post.creator.username,
                    'creator_company': post.creator.company.name if post.creator.company else 'No Company',
                    'project': post.project.name,
                    'project_company': post.project.company.name,
                    'matches_user_company': post.project.company == request.user.company if request.user.company else False
                })

            return Response(result)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @action(detail=False, methods=['get'])
    def creators(self, request):
        """Get all creators assigned to this company admin's projects"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        # Get projects specifically assigned to this company admin
        admin_projects = Project.objects.filter(company_admin=request.user)

        # Get creators assigned to these projects
        creators = User.objects.filter(
            role='creator',
            assigned_projects__in=admin_projects
        ).distinct().select_related('company')

        creators_data = []
        for creator in creators:
            # Get creator's projects that are assigned to this admin
            creator_projects = creator.assigned_projects.filter(company_admin=request.user)

            # Get creator's posts for this admin's projects
            recent_posts_count = Post.objects.filter(
                creator=creator,
                project__in=creator_projects
            ).count()

            pending_posts_count = Post.objects.filter(
                creator=creator,
                project__in=creator_projects,
                status='submitted'
            ).count()

            creators_data.append({
                'id': creator.id,
                'username': creator.username,
                'full_name': creator.get_full_name() or creator.username,
                'email': creator.email,
                'company_name': creator.company.name if creator.company else 'No Company',
                'projects': [{'id': p.id, 'name': p.name, 'title': p.title} for p in creator_projects],
                'total_posts': recent_posts_count,
                'pending_posts': pending_posts_count,
                'status': 'active' if pending_posts_count > 0 else 'idle'
            })

        return Response(creators_data)

    @action(detail=True, methods=['post'])
    def approve_content(self, request, pk=None):
        """Approve a content submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company_admin=request.user,
                status='submitted'
            )

            # Update post status and review information
            post.status = 'posted'
            post.reviewed_by = request.user
            post.reviewed_at = timezone.now()
            post.review_comments = request.data.get('comments', '')
            post.save()

            return Response({
                'message': 'Content approved successfully',
                'post_id': post.id,
                'new_status': post.status,
                'reviewed_by': request.user.username,
                'reviewed_at': post.reviewed_at.isoformat()
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for approval'}, status=404)

    @action(detail=True, methods=['post'])
    def reject_content(self, request, pk=None):
        """Reject a content submission with reason"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company_admin=request.user,
                status='submitted'
            )

            # Get rejection reason from request
            rejection_reason = request.data.get('reason', '')
            if not rejection_reason.strip():
                return Response({'error': 'Rejection reason is required'}, status=400)

            # Update post status and review information
            post.status = 'rejected'
            post.reviewed_by = request.user
            post.reviewed_at = timezone.now()
            post.review_comments = rejection_reason
            post.save()

            return Response({
                'message': 'Content rejected successfully',
                'post_id': post.id,
                'new_status': post.status,
                'rejection_reason': rejection_reason,
                'reviewed_by': request.user.username,
                'reviewed_at': post.reviewed_at.isoformat()
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for rejection'}, status=404)

    @action(detail=True, methods=['post'])
    def request_changes(self, request, pk=None):
        """Request changes to a content submission with instructions"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error

        try:
            post = Post.objects.get(
                id=pk,
                project__company_admin=request.user,
                status='submitted'
            )

            # Get change instructions from request
            change_instructions = request.data.get('instructions', '')
            if not change_instructions.strip():
                return Response({'error': 'Change instructions are required'}, status=400)

            # Update post status and review information
            post.status = 'rework'
            post.reviewed_by = request.user
            post.reviewed_at = timezone.now()
            post.review_comments = change_instructions
            post.save()

            return Response({
                'message': 'Changes requested successfully',
                'post_id': post.id,
                'new_status': post.status,
                'change_instructions': change_instructions,
                'reviewed_by': request.user.username,
                'reviewed_at': post.reviewed_at.isoformat()
            })
        except Post.DoesNotExist:
            return Response({'error': 'Post not found or not eligible for changes'}, status=404)

    @action(detail=False, methods=['get'])
    def pending_blogs(self, request):
        """List blogs pending review for this admin's company/projects"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error
        admin_projects = Project.objects.filter(company_admin=request.user)
        blogs = Blog.objects.filter(project__in=admin_projects, status='submitted').select_related('author', 'project', 'reviewed_by')
        serializer = BlogSerializer(blogs, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], url_path='approve_blog')
    def approve_blog(self, request, pk=None):
        """Approve a blog submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error
        try:
            blog = Blog.objects.get(id=pk, project__company_admin=request.user, status='submitted')
            serializer = BlogReviewSerializer(blog, data={
                'status': 'approved',
                'review_comments': request.data.get('comments', ''),
                'reviewed_by': request.user.id,
                'reviewed_at': timezone.now()
            }, partial=True, context={'request': request})
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        except Blog.DoesNotExist:
            return Response({'error': 'Blog not found or not eligible for approval'}, status=404)

    @action(detail=True, methods=['post'], url_path='reject_blog')
    def reject_blog(self, request, pk=None):
        """Reject a blog submission with reason"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error
        try:
            blog = Blog.objects.get(id=pk, project__company_admin=request.user, status='submitted')
            reason = request.data.get('reason', '')
            if not reason.strip():
                return Response({'error': 'Rejection reason is required'}, status=400)
            serializer = BlogReviewSerializer(blog, data={
                'status': 'rejected',
                'review_comments': reason,
                'reviewed_by': request.user.id,
                'reviewed_at': timezone.now()
            }, partial=True, context={'request': request})
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        except Blog.DoesNotExist:
            return Response({'error': 'Blog not found or not eligible for rejection'}, status=404)

    @action(detail=True, methods=['post'], url_path='changes_requested')
    def changes_requested(self, request, pk=None):
        """Request changes for a blog submission"""
        permission_error = self._check_company_admin_permission(request)
        if permission_error:
            return permission_error
        try:
            blog = Blog.objects.get(id=pk, project__company_admin=request.user, status='submitted')
            instructions = request.data.get('instructions', '')
            if not instructions.strip():
                return Response({'error': 'Change instructions are required'}, status=400)
            serializer = BlogReviewSerializer(blog, data={
                'status': 'changes_requested',
                'review_comments': instructions,
                'reviewed_by': request.user.id,
                'reviewed_at': timezone.now()
            }, partial=True, context={'request': request})
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        except Blog.DoesNotExist:
            return Response({'error': 'Blog not found or not eligible for change request'}, status=404)


    
def custom_exception_handler(exc, context):
    """Custom exception handler with better logging for authentication issues"""
    response = exception_handler(exc, context)

    if response is not None:
        # Log different types of errors with more context
        request = context.get('request')
        user = getattr(request, 'user', None) if request else None

        if response.status_code == 401:
            print(f"🔒 Unauthorized access attempt - User: {user}, Path: {request.path if request else 'unknown'}")
            print(f"    Error details: {response.data}")
        elif response.status_code == 403:
            print(f"🚫 Forbidden access - User: {user}, Path: {request.path if request else 'unknown'}")
            print(f"    Error details: {response.data}")
        else:
            print(f"⚠️  Validation error (Status {response.status_code}):", response.data)

    return response

def health_check(request):
    return JsonResponse({"status": "ok"})

# Social Media Publishing API Endpoint
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def social_media_publish(request):
    """
    Social Media Publishing API Stub

    This endpoint accepts social media publishing requests and returns a mock response.
    In the future, this will integrate with actual social media platform APIs.

    Expected payload:
    {
        "platform": "facebook|instagram|linkedin",
        "message": "Content text",
        "image": "file or URL",
        "schedule_time": "ISO datetime string (optional)",
        "post_id": "ID of the post being published (optional)",
        "blog_id": "ID of the blog being published (optional)"
    }
    """
    try:
        # Extract data from request
        platform = request.data.get('platform')
        message = request.data.get('message', '')
        image = request.FILES.get('image') or request.data.get('image_url')
        schedule_time = request.data.get('schedule_time')
        post_id = request.data.get('post_id')
        blog_id = request.data.get('blog_id')

        # Validate required fields
        if not platform:
            return Response({
                'error': 'Platform is required',
                'supported_platforms': ['facebook', 'instagram', 'linkedin']
            }, status=400)

        if platform not in ['facebook', 'instagram', 'linkedin']:
            return Response({
                'error': f'Unsupported platform: {platform}',
                'supported_platforms': ['facebook', 'instagram', 'linkedin']
            }, status=400)

        if not message and not image:
            return Response({
                'error': 'Either message or image is required'
            }, status=400)

        # Log the publishing request
        print(f"📱 Social Media Publishing Request:")
        print(f"   Platform: {platform}")
        print(f"   User: {request.user.username}")
        print(f"   Message: {message[:100]}..." if len(message) > 100 else f"   Message: {message}")
        print(f"   Has Image: {bool(image)}")
        print(f"   Schedule Time: {schedule_time}")
        print(f"   Post ID: {post_id}")
        print(f"   Blog ID: {blog_id}")

        # Mock response based on platform
        platform_responses = {
            'facebook': {
                'platform_post_id': f'fb_{timezone.now().timestamp()}',
                'platform_url': f'https://facebook.com/posts/{timezone.now().timestamp()}',
                'status': 'published' if not schedule_time else 'scheduled'
            },
            'instagram': {
                'platform_post_id': f'ig_{timezone.now().timestamp()}',
                'platform_url': f'https://instagram.com/p/{timezone.now().timestamp()}',
                'status': 'published' if not schedule_time else 'scheduled'
            },
            'linkedin': {
                'platform_post_id': f'li_{timezone.now().timestamp()}',
                'platform_url': f'https://linkedin.com/posts/{timezone.now().timestamp()}',
                'status': 'published' if not schedule_time else 'scheduled'
            }
        }

        # Simulate processing time
        import time
        time.sleep(0.5)  # Simulate API call delay

        # Build response
        response_data = {
            'success': True,
            'message': f'Content successfully {"scheduled for" if schedule_time else "published to"} {platform.title()}',
            'platform': platform,
            'published_at': timezone.now().isoformat(),
            'scheduled_for': schedule_time,
            **platform_responses[platform]
        }

        # Add content metadata
        if post_id:
            response_data['source_post_id'] = post_id
        if blog_id:
            response_data['source_blog_id'] = blog_id

        # Add image metadata if present
        if image:
            if hasattr(image, 'name'):  # File upload
                response_data['image_uploaded'] = True
                response_data['image_name'] = image.name
            else:  # URL
                response_data['image_url'] = image

        return Response(response_data, status=200)

    except Exception as e:
        print(f"❌ Error in social_media_publish: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to publish content',
            'details': str(e)
        }, status=500)