.blog-list-container {
  max-width: 800px;
  margin: 32px auto;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}
.blog-card {
  border: 1px solid #eee;
  border-radius: 6px;
  margin-bottom: 18px;
  padding: 16px;
  background: #fafbfc;
}
.blog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.blog-title {
  font-size: 1.2em;
  font-weight: 600;
}
.blog-status {
  color: #fff;
  padding: 2px 12px;
  border-radius: 12px;
  font-size: 0.95em;
  font-weight: 500;
}
.blog-meta {
  font-size: 0.95em;
  color: #666;
  margin-bottom: 8px;
}
.blog-content-preview {
  color: #333;
  margin-bottom: 8px;
}
.blog-review-comments {
  background: #fff3e0;
  border-left: 4px solid #ff9800;
  padding: 8px 12px;
  border-radius: 4px;
  color: #a65c00;
  font-size: 0.98em;
}

.blog-review-comments small {
  color: #666;
  font-style: italic;
}

.blog-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.blog-actions button {
  min-width: auto;
}