import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { Blog } from '../../models/blog.model';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-my-blogs',
  standalone: true,
  imports: [CommonModule, RouterModule, MatButtonModule, MatIconModule],
  templateUrl: './my-blogs.component.html',
  styleUrls: ['./my-blogs.component.css']
})
export class MyBlogsComponent implements OnInit {
  blogs: Blog[] = [];
  isLoading = true;

  constructor(private creatorDashboardService: CreatorDashboardService) {}

  ngOnInit(): void {
    this.creatorDashboardService.getMyBlogs().subscribe({
      next: (blogs: Blog[]) => {
        this.blogs = blogs;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'submitted': return '#ff9800'; // Orange for pending
      case 'approved': return '#4caf50'; // Green
      case 'rejected': return '#f44336'; // Red
      case 'changes_requested': return '#2196f3'; // Blue
      case 'draft': return '#9e9e9e'; // Grey
      case 'posted': return '#673ab7'; // Deep Purple
      case 'scheduled': return '#00bcd4'; // Cyan
      default: return '#9e9e9e'; // Default grey
    }
  }
}