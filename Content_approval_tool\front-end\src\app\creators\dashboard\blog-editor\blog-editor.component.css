.blog-editor-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.toolbar {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.toolbar h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #333;
}

.add-block-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.add-block-btn {
  min-width: auto;
}

.blocks-container {
  min-height: 200px;
}

.block-item {
  margin-bottom: 16px;
}

.block-card {
  transition: box-shadow 0.2s ease;
}

.block-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.cdk-drag-preview .block-card {
  box-shadow: 0 8px 24px rgba(0,0,0,0.2);
  transform: rotate(2deg);
}

.cdk-drag-placeholder {
  opacity: 0.4;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fafafa;
}

.block-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.block-type-icon {
  color: #666;
}

.block-type-label {
  font-weight: 500;
  color: #333;
}

.block-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.drag-handle {
  cursor: grab;
  color: #999;
}

.drag-handle:active {
  cursor: grabbing;
}

.block-content {
  padding: 16px;
}

.full-width {
  width: 100%;
}

.image-block {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.image-upload {
  display: flex;
  justify-content: center;
}

.image-preview {
  display: flex;
  justify-content: center;
}

.block-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.code-textarea {
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #999;
}

.empty-state p {
  margin: 0;
  color: #bbb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .blog-editor-container {
    padding: 16px;
  }
  
  .add-block-buttons {
    flex-direction: column;
  }
  
  .add-block-btn {
    width: 100%;
  }
  
  .block-header {
    padding: 8px 12px;
  }
  
  .block-content {
    padding: 12px;
  }
}
