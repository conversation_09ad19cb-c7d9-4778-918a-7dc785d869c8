{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/module-C9K6ZqpI.mjs", "../../../../../../node_modules/@angular/material/fesm2022/tooltip.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Ng<PERSON><PERSON>, Injector, ViewContainerRef, afterNextRender, Directive, Input, ChangeDetectorRef, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { AriaDescriber, FocusMonitor, A11yModule } from '@angular/cdk/a11y';\nimport { Overlay, ScrollDispatcher, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { DOCUMENT, NgClass } from '@angular/common';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst _c0 = [\"tooltip\"];\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition({\n      scrollThrottle: SCROLL_THROTTLE_MS\n    });\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _ariaDescriber = inject(AriaDescriber);\n  _focusMonitor = inject(FocusMonitor);\n  _dir = inject(Directionality);\n  _injector = inject(Injector);\n  _viewContainerRef = inject(ViewContainerRef);\n  _defaultOptions = inject(MAT_TOOLTIP_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _overlayRef;\n  _tooltipInstance;\n  _portal;\n  _position = 'below';\n  _positionAtOrigin = false;\n  _disabled = false;\n  _tooltipClass;\n  _viewInitialized = false;\n  _pointerExitEventsInitialized = false;\n  _tooltipComponent = TooltipComponent;\n  _viewportMargin = 8;\n  _currentPosition;\n  _cssClassPrefix = 'mat-mdc';\n  _ariaDescriptionPending;\n  _dirSubscribed = false;\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    if (value !== this._position) {\n      this._position = value;\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n        this._tooltipInstance?.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  /**\n   * Whether tooltip should be relative to the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  get positionAtOrigin() {\n    return this._positionAtOrigin;\n  }\n  set positionAtOrigin(value) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n  /** Disables the display of the tooltip. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    const isDisabled = coerceBooleanProperty(value);\n    if (this._disabled !== isDisabled) {\n      this._disabled = isDisabled;\n      // If tooltip is disabled, hide immediately.\n      if (isDisabled) {\n        this.hide(0);\n      } else {\n        this._setupPointerEnterEventsIfNeeded();\n      }\n      this._syncAriaDescription(this.message);\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n  get showDelay() {\n    return this._showDelay;\n  }\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  _showDelay;\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  get hideDelay() {\n    return this._hideDelay;\n  }\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n  _hideDelay;\n  /**\n   * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n   * uses a long press gesture to show and hide, however it can conflict with the native browser\n   * gestures. To work around the conflict, Angular Material disables native gestures on the\n   * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n   * elements). The different values for this option configure the touch event handling as follows:\n   * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n   *   browser gestures on particular elements. In particular, it allows text selection on inputs\n   *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n   * - `on` - Enables touch gestures for all elements and disables native\n   *   browser gestures with no exceptions.\n   * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n   *   showing on touch devices.\n   */\n  touchGestures = 'auto';\n  /** The message to be displayed in the tooltip */\n  get message() {\n    return this._message;\n  }\n  set message(value) {\n    const oldMessage = this._message;\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n    }\n    this._syncAriaDescription(oldMessage);\n  }\n  _message = '';\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n  /** Manually-bound passive event listeners. */\n  _passiveListeners = [];\n  /** Timer started at the last `touchstart` event. */\n  _touchstartTimeout = null;\n  /** Emits when the component is destroyed. */\n  _destroyed = new Subject();\n  /** Whether ngOnDestroyed has been called. */\n  _isDestroyed = false;\n  constructor() {\n    const defaultOptions = this._defaultOptions;\n    if (defaultOptions) {\n      this._showDelay = defaultOptions.showDelay;\n      this._hideDelay = defaultOptions.hideDelay;\n      if (defaultOptions.position) {\n        this.position = defaultOptions.position;\n      }\n      if (defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = defaultOptions.positionAtOrigin;\n      }\n      if (defaultOptions.touchGestures) {\n        this.touchGestures = defaultOptions.touchGestures;\n      }\n      if (defaultOptions.tooltipClass) {\n        this.tooltipClass = defaultOptions.tooltipClass;\n      }\n    }\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    // Optimization: Do not call clearTimeout unless there is an active timer.\n    if (this._touchstartTimeout) {\n      clearTimeout(this._touchstartTimeout);\n    }\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._isDestroyed = true;\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay = this.showDelay, origin) {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = this._tooltipInstance = overlayRef.attach(this._portal).instance;\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay = this.hideDelay) {\n    const instance = this._tooltipInstance;\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n  /** Shows/hides the tooltip */\n  toggle(origin) {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n  _createOverlay(origin) {\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig().positionStrategy;\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n      this._detach();\n    }\n    const scrollableAncestors = this._injector.get(ScrollDispatcher).getAncestorScrollContainers(this._elementRef);\n    const overlay = this._injector.get(Overlay);\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = overlay.position().flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._injector.get(MAT_TOOLTIP_SCROLL_STRATEGY)()\n    });\n    this._updatePosition(this._overlayRef);\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n    if (!this._dirSubscribed) {\n      this._dirSubscribed = true;\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (this._overlayRef) {\n          this._updatePosition(this._overlayRef);\n        }\n      });\n    }\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n    position.withPositions([this._addOffset({\n      ...origin.main,\n      ...overlay.main\n    }), this._addOffset({\n      ...origin.fallback,\n      ...overlay.fallback\n    })]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  _addOffset(position) {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n      afterNextRender(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n  /** Updates the tooltip class */\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition;\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    }\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', event => {\n        this._setupPointerExitEventsIfNeeded();\n        let point = undefined;\n        if (event.x !== undefined && event.y !== undefined) {\n          point = event;\n        }\n        this.show(undefined, point);\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      this._passiveListeners.push(['touchstart', event => {\n        const touch = event.targetTouches?.[0];\n        const origin = touch ? {\n          x: touch.clientX,\n          y: touch.clientY\n        } : undefined;\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        const DEFAULT_LONGPRESS_DELAY = 500;\n        this._touchstartTimeout = setTimeout(() => {\n          this._touchstartTimeout = null;\n          this.show(undefined, origin);\n        }, this._defaultOptions?.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n      }]);\n    }\n    this._addListeners(this._passiveListeners);\n  }\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', event => {\n        const newTarget = event.relatedTarget;\n        if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n          this.hide();\n        }\n      }], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        this.hide(this._defaultOptions?.touchendHideDelay);\n      };\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._injector.get(DOCUMENT).elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      }\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n  /** Updates the tooltip's ARIA description based on it current state. */\n  _syncAriaDescription(oldMessage) {\n    if (this._ariaDescriptionPending) {\n      return;\n    }\n    this._ariaDescriptionPending = true;\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, oldMessage, 'tooltip');\n    // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n    // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n    // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n    // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n    if (!this._isDestroyed) {\n      afterNextRender({\n        write: () => {\n          this._ariaDescriptionPending = false;\n          if (this.message && !this.disabled) {\n            this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n          }\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n  static ɵfac = function MatTooltip_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTooltip)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTooltip,\n    selectors: [[\"\", \"matTooltip\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-tooltip-trigger\"],\n    hostVars: 2,\n    hostBindings: function MatTooltip_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tooltip-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      position: [0, \"matTooltipPosition\", \"position\"],\n      positionAtOrigin: [0, \"matTooltipPositionAtOrigin\", \"positionAtOrigin\"],\n      disabled: [0, \"matTooltipDisabled\", \"disabled\"],\n      showDelay: [0, \"matTooltipShowDelay\", \"showDelay\"],\n      hideDelay: [0, \"matTooltipHideDelay\", \"hideDelay\"],\n      touchGestures: [0, \"matTooltipTouchGestures\", \"touchGestures\"],\n      message: [0, \"matTooltip\", \"message\"],\n      tooltipClass: [0, \"matTooltipClass\", \"tooltipClass\"]\n    },\n    exportAs: [\"matTooltip\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-mdc-tooltip-trigger',\n        '[class.mat-mdc-tooltip-disabled]': 'disabled'\n      }\n    }]\n  }], () => [], {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    positionAtOrigin: [{\n      type: Input,\n      args: ['matTooltipPositionAtOrigin']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  /* Whether the tooltip text overflows to multiple lines */\n  _isMultiline = false;\n  /** Message to display in the tooltip */\n  message;\n  /** Classes to be added to the tooltip. Supports the same syntax as `ngClass`. */\n  tooltipClass;\n  /** The timeout ID of any current timer set to show the tooltip */\n  _showTimeoutId;\n  /** The timeout ID of any current timer set to hide the tooltip */\n  _hideTimeoutId;\n  /** Element that caused the tooltip to open. */\n  _triggerElement;\n  /** Amount of milliseconds to delay the closing sequence. */\n  _mouseLeaveHideDelay;\n  /** Whether animations are currently disabled. */\n  _animationsDisabled;\n  /** Reference to the internal tooltip element. */\n  _tooltip;\n  /** Whether interactions on the page should close the tooltip */\n  _closeOnInteraction = false;\n  /** Whether the tooltip is currently visible. */\n  _isVisible = false;\n  /** Subject for notifying that the tooltip has been hidden from the view */\n  _onHide = new Subject();\n  /** Name of the show animation and the class that toggles it. */\n  _showAnimation = 'mat-mdc-tooltip-show';\n  /** Name of the hide animation and the class that toggles it. */\n  _hideAnimation = 'mat-mdc-tooltip-hide';\n  constructor() {\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n  isVisible() {\n    return this._isVisible;\n  }\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null;\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  _handleMouseLeave({\n    relatedTarget\n  }) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  _onShow() {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n  /** Whether the tooltip text has overflown to the next line */\n  _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({\n    animationName\n  }) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n  /** Handles the cleanup after an animation has finished. */\n  _finalizeAnimation(toVisible) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n  /** Toggles the visibility of the tooltip element. */\n  _toggleVisibility(isVisible) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    if (this._isVisible !== isVisible) {\n      this._isVisible = isVisible;\n      this._changeDetectorRef.markForCheck();\n    }\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (styles.getPropertyValue('animation-duration') === '0s' || styles.getPropertyValue('animation-name') === 'none') {\n        this._animationsDisabled = true;\n      }\n    }\n    if (isVisible) {\n      this._onShow();\n    }\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n  static ɵfac = function TooltipComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TooltipComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TooltipComponent,\n    selectors: [[\"mat-tooltip-component\"]],\n    viewQuery: function TooltipComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tooltip = _t.first);\n      }\n    },\n    hostAttrs: [\"aria-hidden\", \"true\"],\n    hostBindings: function TooltipComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mouseleave\", function TooltipComponent_mouseleave_HostBindingHandler($event) {\n          return ctx._handleMouseLeave($event);\n        });\n      }\n    },\n    decls: 4,\n    vars: 4,\n    consts: [[\"tooltip\", \"\"], [1, \"mdc-tooltip\", \"mat-mdc-tooltip\", 3, \"animationend\", \"ngClass\"], [1, \"mat-mdc-tooltip-surface\", \"mdc-tooltip__surface\"]],\n    template: function TooltipComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵlistener(\"animationend\", function TooltipComponent_Template_div_animationend_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleAnimationEnd($event));\n        });\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-tooltip--multiline\", ctx._isMultiline);\n        i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.message);\n      }\n    },\n    dependencies: [NgClass],\n    styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '(mouseleave)': '_handleMouseLeave($event)',\n        'aria-hidden': 'true'\n      },\n      imports: [NgClass],\n      template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\",\n      styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\\n\"]\n    }]\n  }], () => [], {\n    _tooltip: [{\n      type: ViewChild,\n      args: ['tooltip', {\n        // Use a static query here since we interact directly with\n        // the DOM which can happen before `ngAfterViewInit`.\n        static: true\n      }]\n    }]\n  });\n})();\nclass MatTooltipModule {\n  static ɵfac = function MatTooltipModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTooltipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTooltipModule,\n    imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n    imports: [A11yModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MAT_TOOLTIP_SCROLL_STRATEGY as M, SCROLL_THROTTLE_MS as S, TOOLTIP_PANEL_CLASS as T, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY as a, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER as b, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY as c, MAT_TOOLTIP_DEFAULT_OPTIONS as d, MatTooltip as e, TooltipComponent as f, getMatTooltipInvalidPositionError as g, MatTooltipModule as h };\n", "export { d as MAT_TOOLTIP_DEFAULT_OPTIONS, c as MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, M as MAT_TOOLTIP_SCROLL_STRATEGY, a as MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, b as MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, e as <PERSON><PERSON><PERSON>tip, h as MatTooltipModule, S as SCROLL_THROTTLE_MS, T as TOOLTIP_PANEL_CLASS, f as TooltipComponent, g as getMatTooltipInvalidPositionError } from './module-C9K6ZqpI.mjs';\nimport '@angular/core';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport 'rxjs/operators';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/keycodes';\nimport '@angular/common';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/portal';\nimport 'rxjs';\nimport './common-module-WayjW0Pb.mjs';\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nconst matTooltipAnimations = {\n  // Represents:\n  // trigger('state', [\n  //   state('initial, void, hidden', style({opacity: 0, transform: 'scale(0.8)'})),\n  //   state('visible', style({transform: 'scale(1)'})),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n  // ])\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: {\n    type: 7,\n    name: 'state',\n    definitions: [{\n      type: 0,\n      name: 'initial, void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'scale(0.8)'\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(1)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => visible',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => hidden',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '75ms cubic-bezier(0.4, 0, 1, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { matTooltipAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,qBAAqB;AAK3B,SAAS,kCAAkC,UAAU;AACnD,SAAO,MAAM,qBAAqB,QAAQ,eAAe;AAC3D;AAEA,IAAM,8BAA8B,IAAI,eAAe,+BAA+B;AAAA,EACpF,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,MAC/C,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AACF,CAAC;AAMD,SAAS,oCAAoC,SAAS;AACpD,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,IAC/C,gBAAgB;AAAA,EAClB,CAAC;AACH;AAMA,IAAM,+CAA+C;AAAA,EACnD,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AAMA,SAAS,sCAAsC;AAC7C,SAAO;AAAA,IACL,WAAW;AAAA,IACX,WAAW;AAAA,IACX,mBAAmB;AAAA,EACrB;AACF;AAEA,IAAM,8BAA8B,IAAI,eAAe,+BAA+B;AAAA,EACpF,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,IAAM,sBAAsB;AAC5B,IAAM,cAAc;AAEpB,IAAM,yBAAyB,gCAAgC;AAAA,EAC7D,SAAS;AACX,CAAC;AAGD,IAAM,iCAAiC;AACvC,IAAM,uBAAuB;AAC7B,IAAM,aAAa;AACnB,IAAM,YAAY;AAOlB,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,iBAAiB,OAAO,aAAa;AAAA,EACrC,gBAAgB,OAAO,YAAY;AAAA,EACnC,OAAO,OAAO,cAAc;AAAA,EAC5B,YAAY,OAAO,QAAQ;AAAA,EAC3B,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,kBAAkB,OAAO,6BAA6B;AAAA,IACpD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,YAAY;AAAA,EACZ;AAAA,EACA,mBAAmB;AAAA,EACnB,gCAAgC;AAAA,EAChC,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB;AAAA,EACA,kBAAkB;AAAA,EAClB;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,UAAU,KAAK,WAAW;AAC5B,WAAK,YAAY;AACjB,UAAI,KAAK,aAAa;AACpB,aAAK,gBAAgB,KAAK,WAAW;AACrC,aAAK,kBAAkB,KAAK,CAAC;AAC7B,aAAK,YAAY,eAAe;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,SAAK,oBAAoB,sBAAsB,KAAK;AACpD,SAAK,QAAQ;AACb,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,UAAM,aAAa,sBAAsB,KAAK;AAC9C,QAAI,KAAK,cAAc,YAAY;AACjC,WAAK,YAAY;AAEjB,UAAI,YAAY;AACd,aAAK,KAAK,CAAC;AAAA,MACb,OAAO;AACL,aAAK,iCAAiC;AAAA,MACxC;AACA,WAAK,qBAAqB,KAAK,OAAO;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,qBAAqB,KAAK;AAAA,EAC9C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,qBAAqB,KAAK;AAC5C,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,uBAAuB,KAAK;AAAA,IACpD;AAAA,EACF;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,gBAAgB;AAAA;AAAA,EAEhB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,UAAM,aAAa,KAAK;AAIxB,SAAK,WAAW,SAAS,OAAO,OAAO,KAAK,EAAE,KAAK,IAAI;AACvD,QAAI,CAAC,KAAK,YAAY,KAAK,kBAAkB,GAAG;AAC9C,WAAK,KAAK,CAAC;AAAA,IACb,OAAO;AACL,WAAK,iCAAiC;AACtC,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,qBAAqB,UAAU;AAAA,EACtC;AAAA,EACA,WAAW;AAAA;AAAA,EAEX,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,aAAa,OAAO;AACtB,SAAK,gBAAgB;AACrB,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,KAAK,aAAa;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,CAAC;AAAA;AAAA,EAErB,qBAAqB;AAAA;AAAA,EAErB,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,eAAe;AAAA,EACf,cAAc;AACZ,UAAM,iBAAiB,KAAK;AAC5B,QAAI,gBAAgB;AAClB,WAAK,aAAa,eAAe;AACjC,WAAK,aAAa,eAAe;AACjC,UAAI,eAAe,UAAU;AAC3B,aAAK,WAAW,eAAe;AAAA,MACjC;AACA,UAAI,eAAe,kBAAkB;AACnC,aAAK,mBAAmB,eAAe;AAAA,MACzC;AACA,UAAI,eAAe,eAAe;AAChC,aAAK,gBAAgB,eAAe;AAAA,MACtC;AACA,UAAI,eAAe,cAAc;AAC/B,aAAK,eAAe,eAAe;AAAA,MACrC;AAAA,IACF;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,kBAAkB;AAEhB,SAAK,mBAAmB;AACxB,SAAK,iCAAiC;AACtC,SAAK,cAAc,QAAQ,KAAK,WAAW,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAEhG,UAAI,CAAC,QAAQ;AACX,aAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,MACrC,WAAW,WAAW,YAAY;AAChC,aAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,UAAM,gBAAgB,KAAK,YAAY;AAEvC,QAAI,KAAK,oBAAoB;AAC3B,mBAAa,KAAK,kBAAkB;AAAA,IACtC;AACA,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAQ;AACzB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,SAAK,kBAAkB,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM;AACpD,oBAAc,oBAAoB,OAAO,UAAU,sBAAsB;AAAA,IAC3E,CAAC;AACD,SAAK,kBAAkB,SAAS;AAChC,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,eAAe;AACpB,SAAK,eAAe,kBAAkB,eAAe,KAAK,SAAS,SAAS;AAC5E,SAAK,cAAc,eAAe,aAAa;AAAA,EACjD;AAAA;AAAA,EAEA,KAAK,QAAQ,KAAK,WAAW,QAAQ;AACnC,QAAI,KAAK,YAAY,CAAC,KAAK,WAAW,KAAK,kBAAkB,GAAG;AAC9D,WAAK,kBAAkB,yBAAyB;AAChD;AAAA,IACF;AACA,UAAM,aAAa,KAAK,eAAe,MAAM;AAC7C,SAAK,QAAQ;AACb,SAAK,UAAU,KAAK,WAAW,IAAI,gBAAgB,KAAK,mBAAmB,KAAK,iBAAiB;AACjG,UAAM,WAAW,KAAK,mBAAmB,WAAW,OAAO,KAAK,OAAO,EAAE;AACzE,aAAS,kBAAkB,KAAK,YAAY;AAC5C,aAAS,uBAAuB,KAAK;AACrC,aAAS,YAAY,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AACtF,SAAK,iBAAiB,KAAK,aAAa;AACxC,SAAK,sBAAsB;AAC3B,aAAS,KAAK,KAAK;AAAA,EACrB;AAAA;AAAA,EAEA,KAAK,QAAQ,KAAK,WAAW;AAC3B,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACZ,UAAI,SAAS,UAAU,GAAG;AACxB,iBAAS,KAAK,KAAK;AAAA,MACrB,OAAO;AACL,iBAAS,yBAAyB;AAClC,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,QAAQ;AACb,SAAK,kBAAkB,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,QAAW,MAAM;AAAA,EACtE;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,CAAC,CAAC,KAAK,oBAAoB,KAAK,iBAAiB,UAAU;AAAA,EACpE;AAAA;AAAA,EAEA,eAAe,QAAQ;AACrB,QAAI,KAAK,aAAa;AACpB,YAAM,mBAAmB,KAAK,YAAY,UAAU,EAAE;AACtD,WAAK,CAAC,KAAK,oBAAoB,CAAC,WAAW,iBAAiB,mBAAmB,YAAY;AACzF,eAAO,KAAK;AAAA,MACd;AACA,WAAK,QAAQ;AAAA,IACf;AACA,UAAM,sBAAsB,KAAK,UAAU,IAAI,gBAAgB,EAAE,4BAA4B,KAAK,WAAW;AAC7G,UAAM,UAAU,KAAK,UAAU,IAAI,OAAO;AAE1C,UAAM,WAAW,QAAQ,SAAS,EAAE,oBAAoB,KAAK,mBAAmB,UAAU,KAAK,cAAc,KAAK,WAAW,EAAE,sBAAsB,IAAI,KAAK,eAAe,UAAU,EAAE,uBAAuB,KAAK,EAAE,mBAAmB,KAAK,eAAe,EAAE,yBAAyB,mBAAmB;AAC5S,aAAS,gBAAgB,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,YAAU;AAC5E,WAAK,4BAA4B,OAAO,cAAc;AACtD,UAAI,KAAK,kBAAkB;AACzB,YAAI,OAAO,yBAAyB,oBAAoB,KAAK,iBAAiB,UAAU,GAAG;AAGzF,eAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,QACrC;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,cAAc,QAAQ,OAAO;AAAA,MAChC,WAAW,KAAK;AAAA,MAChB,kBAAkB;AAAA,MAClB,YAAY,GAAG,KAAK,eAAe,IAAI,WAAW;AAAA,MAClD,gBAAgB,KAAK,UAAU,IAAI,2BAA2B,EAAE;AAAA,IAClE,CAAC;AACD,SAAK,gBAAgB,KAAK,WAAW;AACrC,SAAK,YAAY,YAAY,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,QAAQ,CAAC;AAC9F,SAAK,YAAY,qBAAqB,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,kBAAkB,uBAAuB,CAAC;AACxI,SAAK,YAAY,cAAc,EAAE,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AACnF,UAAI,KAAK,kBAAkB,KAAK,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,GAAG;AAClF,cAAM,eAAe;AACrB,cAAM,gBAAgB;AACtB,aAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,QAAI,KAAK,iBAAiB,6BAA6B;AACrD,WAAK,YAAY,cAAc,GAAG,KAAK,eAAe,gCAAgC;AAAA,IACxF;AACA,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB;AACtB,WAAK,KAAK,OAAO,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAChE,YAAI,KAAK,aAAa;AACpB,eAAK,gBAAgB,KAAK,WAAW;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,eAAe,KAAK,YAAY,YAAY,GAAG;AACtD,WAAK,YAAY,OAAO;AAAA,IAC1B;AACA,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,gBAAgB,YAAY;AAC1B,UAAM,WAAW,WAAW,UAAU,EAAE;AACxC,UAAM,SAAS,KAAK,WAAW;AAC/B,UAAM,UAAU,KAAK,oBAAoB;AACzC,aAAS,cAAc,CAAC,KAAK,WAAW,kCACnC,OAAO,OACP,QAAQ,KACZ,GAAG,KAAK,WAAW,kCACf,OAAO,WACP,QAAQ,SACZ,CAAC,CAAC;AAAA,EACL;AAAA;AAAA,EAEA,WAAW,UAAU;AACnB,UAAM,SAAS;AACf,UAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC/C,QAAI,SAAS,YAAY,OAAO;AAC9B,eAAS,UAAU,CAAC;AAAA,IACtB,WAAW,SAAS,YAAY,UAAU;AACxC,eAAS,UAAU;AAAA,IACrB,WAAW,SAAS,YAAY,SAAS;AACvC,eAAS,UAAU,QAAQ,CAAC,SAAS;AAAA,IACvC,WAAW,SAAS,YAAY,OAAO;AACrC,eAAS,UAAU,QAAQ,SAAS,CAAC;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,UAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC/C,UAAM,WAAW,KAAK;AACtB,QAAI;AACJ,QAAI,YAAY,WAAW,YAAY,SAAS;AAC9C,uBAAiB;AAAA,QACf,SAAS;AAAA,QACT,SAAS,YAAY,UAAU,QAAQ;AAAA,MACzC;AAAA,IACF,WAAW,YAAY,YAAY,YAAY,UAAU,SAAS,YAAY,WAAW,CAAC,OAAO;AAC/F,uBAAiB;AAAA,QACf,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF,WAAW,YAAY,WAAW,YAAY,WAAW,SAAS,YAAY,UAAU,CAAC,OAAO;AAC9F,uBAAiB;AAAA,QACf,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,YAAM,kCAAkC,QAAQ;AAAA,IAClD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,gBAAgB,eAAe,SAAS,eAAe,OAAO;AACvE,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,KAAK,SAAS;AAC/C,UAAM,WAAW,KAAK;AACtB,QAAI;AACJ,QAAI,YAAY,SAAS;AACvB,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,YAAY,SAAS;AAC9B,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,YAAY,YAAY,YAAY,UAAU,SAAS,YAAY,WAAW,CAAC,OAAO;AAC/F,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,YAAY,WAAW,YAAY,WAAW,SAAS,YAAY,UAAU,CAAC,OAAO;AAC9F,wBAAkB;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF,WAAW,OAAO,cAAc,eAAe,WAAW;AACxD,YAAM,kCAAkC,QAAQ;AAAA,IAClD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,gBAAgB,gBAAgB,UAAU,gBAAgB,QAAQ;AAC3E,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AAGtB,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,UAAU,KAAK;AACrC,WAAK,iBAAiB,cAAc;AACpC,sBAAgB,MAAM;AACpB,YAAI,KAAK,kBAAkB;AACzB,eAAK,YAAY,eAAe;AAAA,QAClC;AAAA,MACF,GAAG;AAAA,QACD,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,cAAc;AAC7B,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,eAAe;AACrC,WAAK,iBAAiB,cAAc;AAAA,IACtC;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,GAAG,GAAG;AACpB,QAAI,KAAK,aAAa,WAAW,KAAK,aAAa,SAAS;AAC1D,UAAI,MAAM,OAAO;AACf,YAAI;AAAA,MACN,WAAW,MAAM,UAAU;AACzB,YAAI;AAAA,MACN;AAAA,IACF,OAAO;AACL,UAAI,MAAM,OAAO;AACf,YAAI;AAAA,MACN,WAAW,MAAM,SAAS;AACxB,YAAI;AAAA,MACN;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,4BAA4B,gBAAgB;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI;AAGJ,QAAI,aAAa,UAAU;AAIzB,UAAI,KAAK,QAAQ,KAAK,KAAK,UAAU,OAAO;AAC1C,sBAAc,YAAY,QAAQ,SAAS;AAAA,MAC7C,OAAO;AACL,sBAAc,YAAY,UAAU,SAAS;AAAA,MAC/C;AAAA,IACF,OAAO;AACL,oBAAc,aAAa,YAAY,YAAY,QAAQ,UAAU;AAAA,IACvE;AACA,QAAI,gBAAgB,KAAK,kBAAkB;AACzC,YAAM,aAAa,KAAK;AACxB,UAAI,YAAY;AACd,cAAM,cAAc,GAAG,KAAK,eAAe,IAAI,WAAW;AAC1D,mBAAW,iBAAiB,cAAc,KAAK,gBAAgB;AAC/D,mBAAW,cAAc,cAAc,WAAW;AAAA,MACpD;AACA,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA,EAEA,mCAAmC;AAEjC,QAAI,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC,KAAK,oBAAoB,KAAK,kBAAkB,QAAQ;AAC9F;AAAA,IACF;AAGA,QAAI,KAAK,6BAA6B,GAAG;AACvC,WAAK,kBAAkB,KAAK,CAAC,cAAc,WAAS;AAClD,aAAK,gCAAgC;AACrC,YAAI,QAAQ;AACZ,YAAI,MAAM,MAAM,UAAa,MAAM,MAAM,QAAW;AAClD,kBAAQ;AAAA,QACV;AACA,aAAK,KAAK,QAAW,KAAK;AAAA,MAC5B,CAAC,CAAC;AAAA,IACJ,WAAW,KAAK,kBAAkB,OAAO;AACvC,WAAK,kCAAkC;AACvC,WAAK,kBAAkB,KAAK,CAAC,cAAc,WAAS;AAClD,cAAM,QAAQ,MAAM,gBAAgB,CAAC;AACrC,cAAM,SAAS,QAAQ;AAAA,UACrB,GAAG,MAAM;AAAA,UACT,GAAG,MAAM;AAAA,QACX,IAAI;AAGJ,aAAK,gCAAgC;AACrC,YAAI,KAAK,oBAAoB;AAC3B,uBAAa,KAAK,kBAAkB;AAAA,QACtC;AACA,cAAM,0BAA0B;AAChC,aAAK,qBAAqB,WAAW,MAAM;AACzC,eAAK,qBAAqB;AAC1B,eAAK,KAAK,QAAW,MAAM;AAAA,QAC7B,GAAG,KAAK,iBAAiB,2BAA2B,uBAAuB;AAAA,MAC7E,CAAC,CAAC;AAAA,IACJ;AACA,SAAK,cAAc,KAAK,iBAAiB;AAAA,EAC3C;AAAA,EACA,kCAAkC;AAChC,QAAI,KAAK,+BAA+B;AACtC;AAAA,IACF;AACA,SAAK,gCAAgC;AACrC,UAAM,gBAAgB,CAAC;AACvB,QAAI,KAAK,6BAA6B,GAAG;AACvC,oBAAc,KAAK,CAAC,cAAc,WAAS;AACzC,cAAM,YAAY,MAAM;AACxB,YAAI,CAAC,aAAa,CAAC,KAAK,aAAa,eAAe,SAAS,SAAS,GAAG;AACvE,eAAK,KAAK;AAAA,QACZ;AAAA,MACF,CAAC,GAAG,CAAC,SAAS,WAAS,KAAK,eAAe,KAAK,CAAC,CAAC;AAAA,IACpD,WAAW,KAAK,kBAAkB,OAAO;AACvC,WAAK,kCAAkC;AACvC,YAAM,mBAAmB,MAAM;AAC7B,YAAI,KAAK,oBAAoB;AAC3B,uBAAa,KAAK,kBAAkB;AAAA,QACtC;AACA,aAAK,KAAK,KAAK,iBAAiB,iBAAiB;AAAA,MACnD;AACA,oBAAc,KAAK,CAAC,YAAY,gBAAgB,GAAG,CAAC,eAAe,gBAAgB,CAAC;AAAA,IACtF;AACA,SAAK,cAAc,aAAa;AAChC,SAAK,kBAAkB,KAAK,GAAG,aAAa;AAAA,EAC9C;AAAA,EACA,cAAc,WAAW;AACvB,cAAU,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM;AACvC,WAAK,YAAY,cAAc,iBAAiB,OAAO,UAAU,sBAAsB;AAAA,IACzF,CAAC;AAAA,EACH;AAAA,EACA,+BAA+B;AAC7B,WAAO,CAAC,KAAK,UAAU,OAAO,CAAC,KAAK,UAAU;AAAA,EAChD;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,QAAI,KAAK,kBAAkB,GAAG;AAC5B,YAAM,sBAAsB,KAAK,UAAU,IAAI,QAAQ,EAAE,iBAAiB,MAAM,SAAS,MAAM,OAAO;AACtG,YAAM,UAAU,KAAK,YAAY;AAKjC,UAAI,wBAAwB,WAAW,CAAC,QAAQ,SAAS,mBAAmB,GAAG;AAC7E,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,oCAAoC;AAClC,UAAM,WAAW,KAAK;AACtB,QAAI,aAAa,OAAO;AACtB,YAAM,UAAU,KAAK,YAAY;AACjC,YAAM,QAAQ,QAAQ;AAGtB,UAAI,aAAa,QAAQ,QAAQ,aAAa,WAAW,QAAQ,aAAa,YAAY;AACxF,cAAM,aAAa,MAAM,eAAe,MAAM,mBAAmB,MAAM,gBAAgB;AAAA,MACzF;AAGA,UAAI,aAAa,QAAQ,CAAC,QAAQ,WAAW;AAC3C,cAAM,iBAAiB;AAAA,MACzB;AACA,YAAM,cAAc;AACpB,YAAM,0BAA0B;AAAA,IAClC;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB,YAAY;AAC/B,QAAI,KAAK,yBAAyB;AAChC;AAAA,IACF;AACA,SAAK,0BAA0B;AAC/B,SAAK,eAAe,kBAAkB,KAAK,YAAY,eAAe,YAAY,SAAS;AAK3F,QAAI,CAAC,KAAK,cAAc;AACtB,sBAAgB;AAAA,QACd,OAAO,MAAM;AACX,eAAK,0BAA0B;AAC/B,cAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,iBAAK,eAAe,SAAS,KAAK,YAAY,eAAe,KAAK,SAAS,SAAS;AAAA,UACtF;AAAA,QACF;AAAA,MACF,GAAG;AAAA,QACD,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,IAClC,WAAW,CAAC,GAAG,yBAAyB;AAAA,IACxC,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,4BAA4B,IAAI,QAAQ;AAAA,MACzD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,MAC9C,kBAAkB,CAAC,GAAG,8BAA8B,kBAAkB;AAAA,MACtE,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,MAC9C,WAAW,CAAC,GAAG,uBAAuB,WAAW;AAAA,MACjD,WAAW,CAAC,GAAG,uBAAuB,WAAW;AAAA,MACjD,eAAe,CAAC,GAAG,2BAA2B,eAAe;AAAA,MAC7D,SAAS,CAAC,GAAG,cAAc,SAAS;AAAA,MACpC,cAAc,CAAC,GAAG,mBAAmB,cAAc;AAAA,IACrD;AAAA,IACA,UAAU,CAAC,YAAY;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,oCAAoC;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,IAClC,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B,eAAe;AAAA;AAAA,EAEf;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,sBAAsB;AAAA;AAAA,EAEtB,aAAa;AAAA;AAAA,EAEb,UAAU,IAAI,QAAQ;AAAA;AAAA,EAEtB,iBAAiB;AAAA;AAAA,EAEjB,iBAAiB;AAAA,EACjB,cAAc;AACZ,UAAM,gBAAgB,OAAO,uBAAuB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,sBAAsB,kBAAkB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO;AAEV,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,SAAK,iBAAiB,WAAW,MAAM;AACrC,WAAK,kBAAkB,IAAI;AAC3B,WAAK,iBAAiB;AAAA,IACxB,GAAG,KAAK;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO;AAEV,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,SAAK,iBAAiB,WAAW,MAAM;AACrC,WAAK,kBAAkB,KAAK;AAC5B,WAAK,iBAAiB;AAAA,IACxB,GAAG,KAAK;AAAA,EACV;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,SAAK,yBAAyB;AAC9B,SAAK,QAAQ,SAAS;AACtB,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB;AACvB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,KAAK,CAAC;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,EACF,GAAG;AACD,QAAI,CAAC,iBAAiB,CAAC,KAAK,gBAAgB,SAAS,aAAa,GAAG;AACnE,UAAI,KAAK,UAAU,GAAG;AACpB,aAAK,KAAK,KAAK,oBAAoB;AAAA,MACrC,OAAO;AACL,aAAK,mBAAmB,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACR,SAAK,eAAe,KAAK,oBAAoB;AAC7C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,OAAO,KAAK,YAAY,cAAc,sBAAsB;AAClE,WAAO,KAAK,SAAS,cAAc,KAAK,SAAS;AAAA,EACnD;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAClB;AAAA,EACF,GAAG;AACD,QAAI,kBAAkB,KAAK,kBAAkB,kBAAkB,KAAK,gBAAgB;AAClF,WAAK,mBAAmB,kBAAkB,KAAK,cAAc;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,QAAI,KAAK,kBAAkB,MAAM;AAC/B,mBAAa,KAAK,cAAc;AAAA,IAClC;AACA,SAAK,iBAAiB,KAAK,iBAAiB;AAAA,EAC9C;AAAA;AAAA,EAEA,mBAAmB,WAAW;AAC5B,QAAI,WAAW;AACb,WAAK,sBAAsB;AAAA,IAC7B,WAAW,CAAC,KAAK,UAAU,GAAG;AAC5B,WAAK,QAAQ,KAAK;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,WAAW;AAI3B,UAAM,UAAU,KAAK,SAAS;AAC9B,UAAM,YAAY,KAAK;AACvB,UAAM,YAAY,KAAK;AACvB,YAAQ,UAAU,OAAO,YAAY,YAAY,SAAS;AAC1D,YAAQ,UAAU,IAAI,YAAY,YAAY,SAAS;AACvD,QAAI,KAAK,eAAe,WAAW;AACjC,WAAK,aAAa;AAClB,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAGA,QAAI,aAAa,CAAC,KAAK,uBAAuB,OAAO,qBAAqB,YAAY;AACpF,YAAM,SAAS,iBAAiB,OAAO;AAEvC,UAAI,OAAO,iBAAiB,oBAAoB,MAAM,QAAQ,OAAO,iBAAiB,gBAAgB,MAAM,QAAQ;AAClH,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,WAAW;AACb,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,KAAK,qBAAqB;AAC5B,cAAQ,UAAU,IAAI,yBAAyB;AAC/C,WAAK,mBAAmB,SAAS;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,IACrC,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,MACjE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,eAAe,MAAM;AAAA,IACjC,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,cAAc,SAAS,+CAA+C,QAAQ;AAC1F,iBAAO,IAAI,kBAAkB,MAAM;AAAA,QACrC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,eAAe,mBAAmB,GAAG,gBAAgB,SAAS,GAAG,CAAC,GAAG,2BAA2B,sBAAsB,CAAC;AAAA,IACrJ,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,gBAAgB,SAAS,sDAAsD,QAAQ;AACnG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,oBAAoB,MAAM,CAAC;AAAA,QACvD,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,OAAO,CAAC;AACX,QAAG,aAAa,EAAE;AAAA,MACpB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,0BAA0B,IAAI,YAAY;AACzD,QAAG,WAAW,WAAW,IAAI,YAAY;AACzC,QAAG,UAAU,CAAC;AACd,QAAG,kBAAkB,IAAI,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,OAAO;AAAA,IACtB,QAAQ,CAAC,yxEAA6xE;AAAA,IACtyE,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,gBAAgB;AAAA,QAChB,eAAe;AAAA,MACjB;AAAA,MACA,SAAS,CAAC,OAAO;AAAA,MACjB,UAAU;AAAA,MACV,QAAQ,CAAC,yxEAA6xE;AAAA,IACxyE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA;AAAA;AAAA,QAGhB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,YAAY,eAAe,iBAAiB,YAAY,gBAAgB;AAAA,IAClF,SAAS,CAAC,YAAY,kBAAkB,iBAAiB,mBAAmB;AAAA,EAC9E,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,4CAA4C;AAAA,IACxD,SAAS,CAAC,YAAY,eAAe,iBAAiB,iBAAiB,mBAAmB;AAAA,EAC5F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,YAAY,eAAe,iBAAiB,YAAY,gBAAgB;AAAA,MAClF,SAAS,CAAC,YAAY,kBAAkB,iBAAiB,mBAAmB;AAAA,MAC5E,WAAW,CAAC,4CAA4C;AAAA,IAC1D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3gCH,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3B,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}