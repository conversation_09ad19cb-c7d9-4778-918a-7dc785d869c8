{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/shadow-dom-B0oHn41l.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/fake-event-detection-DWOdFTFz.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/id-generator-Dw_9dSDu.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/array-I1yfCXUO.mjs"], "sourcesContent": ["let shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\nexport { _getEventTarget as _, _getShadowRoot as a, _supportsShadowDom as b, _getFocusedElementPierceShadowDom as c };\n", "/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\nexport { isFakeTouchstartFromScreenReader as a, isFakeMousedownFromScreenReader as i };\n", "import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n  _appId = inject(APP_ID);\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix) {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n    return `${prefix}${counters[prefix]++}`;\n  }\n  static ɵfac = function _IdGenerator_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _IdGenerator)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _IdGenerator,\n    factory: _IdGenerator.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_IdGenerator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _IdGenerator as _ };\n", "function coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nexport { coerceArray as c };\n"], "mappings": ";;;;;;;;;AAAA,IAAI;AAEJ,SAAS,qBAAqB;AAC5B,MAAI,wBAAwB,MAAM;AAChC,UAAM,OAAO,OAAO,aAAa,cAAc,SAAS,OAAO;AAC/D,2BAAuB,CAAC,EAAE,SAAS,KAAK,oBAAoB,KAAK;AAAA,EACnE;AACA,SAAO;AACT;AAEA,SAAS,eAAe,SAAS;AAC/B,MAAI,mBAAmB,GAAG;AACxB,UAAM,WAAW,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAG/D,QAAI,OAAO,eAAe,eAAe,cAAc,oBAAoB,YAAY;AACrF,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,oCAAoC;AAC3C,MAAI,gBAAgB,OAAO,aAAa,eAAe,WAAW,SAAS,gBAAgB;AAC3F,SAAO,iBAAiB,cAAc,YAAY;AAChD,UAAM,mBAAmB,cAAc,WAAW;AAClD,QAAI,qBAAqB,eAAe;AACtC;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAG9B,SAAO,MAAM,eAAe,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AAC9D;;;ACzCA,SAAS,gCAAgC,OAAO;AAM9C,SAAO,MAAM,YAAY,KAAK,MAAM,WAAW;AACjD;AAEA,SAAS,iCAAiC,OAAO;AAC/C,QAAM,QAAQ,MAAM,WAAW,MAAM,QAAQ,CAAC,KAAK,MAAM,kBAAkB,MAAM,eAAe,CAAC;AAKjG,SAAO,CAAC,CAAC,SAAS,MAAM,eAAe,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY;AAC7I;;;ACTA,IAAM,WAAW,CAAC;AAElB,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,SAAS,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,MAAM,QAAQ;AAGZ,QAAI,KAAK,WAAW,MAAM;AACxB,gBAAU,KAAK;AAAA,IACjB;AACA,QAAI,CAAC,SAAS,eAAe,MAAM,GAAG;AACpC,eAAS,MAAM,IAAI;AAAA,IACrB;AACA,WAAO,GAAG,MAAM,GAAG,SAAS,MAAM,GAAG;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3CH,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;", "names": []}