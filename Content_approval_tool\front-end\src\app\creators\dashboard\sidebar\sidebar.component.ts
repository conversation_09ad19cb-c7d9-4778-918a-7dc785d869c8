import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog } from '@angular/material/dialog';
import { PostDialogComponent } from '../post-dialog/post-dialog.component';
import { CreatorDashboardService } from '../../services/creator-dashboard.service';
import { UserService, CurrentUser } from '../../../shared/services/user.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-creator-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatBadgeModule,
    MatMenuModule,
    MatDividerModule
  ],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css']
})
export class CreatorSidebarComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  isCollapsed = false;
  currentUser: CurrentUser | null = null;
  assignedProjects: any[] = [];

  navigationItems = [
    {
      icon: 'dashboard',
      label: 'Dashboard',
      route: '/creators',
      active: true
    },
    {
      icon: 'calendar_today',
      label: 'Calendar',
      route: '/creators/calendar-view',
      active: false
    },
    {
      icon: 'article',
      label: 'My Posts',
      route: '/creators/post-list',
      active: false,
      badge: 3
    },
    {
      icon: 'library_books',
      label: 'My Blogs',
      route: '/creators/my-blogs',
      active: false
    },
    {
      icon: 'add_circle',
      label: 'Create Post',
      route: '/creators/create-post',
      active: false
    },
    {
      icon: 'add',
      label: 'Create-blog',
      route: '/creators/create-blog',
      active: false
    },

    {
      icon: 'logout',
      label: 'Logout',
      route: '/creators/notifications',
      active: false,
      badge: 0
    },
    {
      icon: 'settings',
      label: 'Settings',
      route: '/creators/settings',
      active: false
    }
  ];

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private creatorDashboardService: CreatorDashboardService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.updateActiveRoute();
    this.loadUserInfo();
    this.loadNotificationCounts();
    this.loadAssignedProjects();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
    this.updateActiveRoute();
  }

  createNewPost(): void {
    // Get the first assigned project as default, or let user select
    const defaultProjectId = this.assignedProjects.length > 0 ? this.assignedProjects[0].id : null;

    if (!defaultProjectId) {
      console.warn('No assigned projects found');
      // Could show a message to user that they need to be assigned to a project
      return;
    }

    // Open post creation dialog instead of navigating
    const dialogRef = this.dialog.open(PostDialogComponent, {
      width: '500px',
      data: {
        date: new Date().toISOString().split('T')[0],
        projectId: defaultProjectId,
        assignedProjects: this.assignedProjects
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Refresh dashboard data after post creation
        this.creatorDashboardService.refreshDashboardData().subscribe();
      }
    });
  }

  logout(): void {
    // Clear user data and tokens
    this.userService.clearUser();
    this.creatorDashboardService.clearCache();
    localStorage.clear();
    this.router.navigate(['/login']);
  }

  private updateActiveRoute(): void {
    const currentRoute = this.router.url;
    this.navigationItems.forEach(item => {
      item.active = currentRoute.includes(item.route);
    });
  }

  private loadUserInfo(): void {
    // Subscribe to current user from UserService
    this.userService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUser = user;
        if (!user) {
          // If no user data, try to fetch from backend
          this.userService.getCurrentUser().subscribe({
            error: (error) => {
              console.error('Error loading user info:', error);
              // If failed, redirect to login
              this.router.navigate(['/login']);
            }
          });
        }
      });
  }

  private loadAssignedProjects(): void {
    this.creatorDashboardService.getAssignedProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (projects) => {
          this.assignedProjects = projects;
          console.log('Assigned projects loaded:', projects);
        },
        error: (error) => {
          console.error('Error loading assigned projects:', error);
        }
      });
  }

  getUserInitials(): string {
    if (!this.currentUser) return 'U';
    return this.userService.getUserInitials(this.currentUser);
  }

  getUserDisplayName(): string {
    if (!this.currentUser) return 'User';
    return this.userService.getUserDisplayName(this.currentUser);
  }

  getUserEmail(): string {
    return this.currentUser?.email || '<EMAIL>';
  }

  formatDeadline(deadline: string): string {
    const deadlineDate = new Date(deadline);
    const now = new Date();
    const diffTime = deadlineDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Overdue';
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else if (diffDays <= 7) {
      return `Due in ${diffDays} days`;
    } else {
      return deadlineDate.toLocaleDateString();
    }
  }

  private loadNotificationCounts(): void {
    // Load notification counts from service
    this.creatorDashboardService.getMyPosts().subscribe(posts => {
      const pendingPosts = posts.filter(post => post.status === 'submitted' || post.status === 'rework');

      // Update badge counts
      const postsItem = this.navigationItems.find(item => item.label === 'My Posts');
      if (postsItem) {
        postsItem.badge = pendingPosts.length;
      }

      // Mock notification count - in real app, this would come from a notifications service
      const notificationsItem = this.navigationItems.find(item => item.label === 'Notifications');
      if (notificationsItem) {
        notificationsItem.badge = 2; // Mock count
      }
    });
  }
}
