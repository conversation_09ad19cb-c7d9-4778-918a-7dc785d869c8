from django.core.management.base import BaseCommand
from core.models import Post
from core.serializers import PostSerializer
from django.test import RequestFactory

class Command(BaseCommand):
    help = 'Check media URLs for posts'

    def handle(self, *args, **options):
        self.stdout.write("=== CHECKING MEDIA URLs ===\n")
        
        # Get all posts
        posts = Post.objects.all()
        
        for post in posts:
            self.stdout.write(f"Post: {post.title}")
            self.stdout.write(f"  Media field: {post.media}")
            if post.media:
                self.stdout.write(f"  Media name: {post.media.name}")
                self.stdout.write(f"  Media url: {post.media.url}")
                self.stdout.write(f"  Media path: {post.media.path}")
                
                # Check if file exists
                import os
                if os.path.exists(post.media.path):
                    self.stdout.write(f"  File exists: YES")
                else:
                    self.stdout.write(f"  File exists: NO")
            else:
                self.stdout.write(f"  No media attached")
            
            # Test serializer
            factory = RequestFactory()
            request = factory.get('/')
            request.META['HTTP_HOST'] = '127.0.0.1:8000'
            request.META['wsgi.url_scheme'] = 'http'
            
            serializer = PostSerializer(post, context={'request': request})
            media_url = serializer.data.get('media_url')
            self.stdout.write(f"  Serialized media_url: {media_url}")
            self.stdout.write("---")
