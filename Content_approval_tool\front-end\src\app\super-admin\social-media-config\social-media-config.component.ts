import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

interface SocialMediaPlatform {
  id: string;
  name: string;
  icon: string;
  enabled: boolean;
  fields: SocialMediaField[];
}

interface SocialMediaField {
  key: string;
  label: string;
  type: 'text' | 'password' | 'textarea';
  required: boolean;
  placeholder: string;
  value: string;
}

interface SocialMediaConfig {
  company_id?: number;
  project_id?: number;
  platform: string;
  credentials: { [key: string]: string };
  enabled: boolean;
}

@Component({
  selector: 'app-social-media-config',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
    MatTabsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './social-media-config.component.html',
  styleUrls: ['./social-media-config.component.css']
})
export class SocialMediaConfigComponent implements OnInit {
  platforms: SocialMediaPlatform[] = [
    {
      id: 'facebook',
      name: 'Facebook',
      icon: 'facebook',
      enabled: false,
      fields: [
        {
          key: 'access_token',
          label: 'Access Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Facebook access token',
          value: ''
        },
        {
          key: 'page_id',
          label: 'Facebook Page ID',
          type: 'text',
          required: true,
          placeholder: 'Enter Facebook page ID',
          value: ''
        },
        {
          key: 'page_access_token',
          label: 'Page Access Token',
          type: 'password',
          required: true,
          placeholder: 'Enter page access token',
          value: ''
        }
      ]
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: 'camera_alt',
      enabled: false,
      fields: [
        {
          key: 'access_token',
          label: 'Access Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Instagram access token',
          value: ''
        },
        {
          key: 'instagram_page_id',
          label: 'Instagram Page ID',
          type: 'text',
          required: true,
          placeholder: 'Enter Instagram page ID',
          value: ''
        }
      ]
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: 'work',
      enabled: false,
      fields: [
        {
          key: 'access_token',
          label: 'Access Token',
          type: 'password',
          required: true,
          placeholder: 'Enter LinkedIn access token',
          value: ''
        },
        {
          key: 'organization_id',
          label: 'Organization ID',
          type: 'text',
          required: false,
          placeholder: 'Enter organization ID (optional)',
          value: ''
        }
      ]
    }
  ];

  companies: any[] = [];
  projects: any[] = [];
  selectedCompany: number | null = null;
  selectedProject: number | null = null;
  configScope: 'global' | 'company' | 'project' = 'global';
  
  isLoading = false;
  isSaving = false;

  constructor(private snackBar: MatSnackBar) {}

  ngOnInit(): void {
    this.loadCompanies();
    this.loadExistingConfigs();
  }

  loadCompanies(): void {
    // TODO: Implement API call to load companies
    // For now, using mock data
    this.companies = [
      { id: 1, name: 'Company A' },
      { id: 2, name: 'Company B' }
    ];
  }

  loadProjects(): void {
    if (!this.selectedCompany) {
      this.projects = [];
      return;
    }
    
    // TODO: Implement API call to load projects for selected company
    // For now, using mock data
    this.projects = [
      { id: 1, name: 'Project 1', company: this.selectedCompany },
      { id: 2, name: 'Project 2', company: this.selectedCompany }
    ];
  }

  loadExistingConfigs(): void {
    // TODO: Implement API call to load existing configurations
    // This would populate the form fields with saved values
  }

  onCompanyChange(): void {
    this.selectedProject = null;
    this.loadProjects();
    this.loadExistingConfigs();
  }

  onProjectChange(): void {
    this.loadExistingConfigs();
  }

  onScopeChange(): void {
    this.selectedCompany = null;
    this.selectedProject = null;
    this.projects = [];
    this.loadExistingConfigs();
  }

  onPlatformToggle(platform: SocialMediaPlatform): void {
    platform.enabled = !platform.enabled;
    if (!platform.enabled) {
      // Clear all field values when disabling
      platform.fields.forEach(field => field.value = '');
    }
  }

  isFormValid(): boolean {
    return this.platforms
      .filter(p => p.enabled)
      .every(platform => 
        platform.fields
          .filter(f => f.required)
          .every(field => field.value.trim() !== '')
      );
  }

  onSave(): void {
    if (!this.isFormValid()) {
      this.showError('Please fill in all required fields for enabled platforms');
      return;
    }

    this.isSaving = true;
    
    const configs: SocialMediaConfig[] = this.platforms
      .filter(p => p.enabled)
      .map(platform => ({
        company_id: this.configScope === 'company' || this.configScope === 'project' ? this.selectedCompany! : undefined,
        project_id: this.configScope === 'project' ? this.selectedProject! : undefined,
        platform: platform.id,
        credentials: platform.fields.reduce((acc, field) => {
          acc[field.key] = field.value;
          return acc;
        }, {} as { [key: string]: string }),
        enabled: true
      }));

    // TODO: Implement API call to save configurations
    console.log('Saving social media configs:', configs);
    
    // Simulate API call
    setTimeout(() => {
      this.isSaving = false;
      this.showSuccess('Social media configurations saved successfully!');
    }, 1000);
  }

  onTestConnection(platform: SocialMediaPlatform): void {
    if (!this.isPlatformConfigValid(platform)) {
      this.showError(`Please fill in all required fields for ${platform.name}`);
      return;
    }

    // TODO: Implement API call to test connection
    this.showSuccess(`Testing connection to ${platform.name}... (Feature coming soon)`);
  }

  isPlatformConfigValid(platform: SocialMediaPlatform): boolean {
    return platform.fields
      .filter(f => f.required)
      .every(field => field.value.trim() !== '');
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
