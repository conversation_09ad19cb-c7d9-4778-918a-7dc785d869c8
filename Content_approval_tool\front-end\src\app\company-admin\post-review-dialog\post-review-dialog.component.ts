import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';
import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';

export interface PostReviewDialogData {
  actionType: 'reject' | 'changes';
}

@Component({
  selector: 'app-post-review-dialog',
  templateUrl: './post-review-dialog.component.html',
  styleUrls: ['./post-review-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    CdkTextareaAutosize,
  ]
})
export class PostReviewDialogComponent {
  reviewComments: string = '';

  constructor(
    public dialogRef: MatDialogRef<PostReviewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PostReviewDialogData
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {
    this.dialogRef.close(this.reviewComments);
  }
} 